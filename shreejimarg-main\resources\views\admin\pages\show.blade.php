@extends('layouts.admin')

@section('title', 'View Page')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ $page->title }}</h1>
            <p class="text-muted">Page Details</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('page.show', $page->slug) }}" target="_blank" class="btn btn-outline-primary">
                <i class="fas fa-external-link-alt me-2"></i>View Live
            </a>
            <a href="{{ route('admin.pages.edit', $page) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Page
            </a>
            <a href="{{ route('admin.pages.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Pages
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Page Content -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Page Content</h5>
                    <div class="d-flex gap-2">
                        @if($page->is_published)
                        <span class="badge bg-success">Published</span>
                        @else
                        <span class="badge bg-warning">Draft</span>
                        @endif
                        <span class="badge bg-secondary">{{ ucfirst($page->template) }}</span>
                    </div>
                </div>
                <div class="card-body">
                    @if($page->featured_image)
                    <div class="text-center mb-4">
                        <img src="{{ $page->featured_image }}" alt="{{ $page->title }}" 
                             class="img-fluid rounded shadow" style="max-height: 300px;">
                    </div>
                    @endif
                    
                    @if($page->excerpt)
                    <div class="alert alert-info">
                        <strong>Excerpt:</strong> {{ $page->excerpt }}
                    </div>
                    @endif
                    
                    <div class="content">
                        {!! $page->content !!}
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">SEO Information</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Meta Title</label>
                            <p class="text-muted">{{ $page->meta_title ?: 'Auto-generated from title' }}</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Slug</label>
                            <p class="text-muted"><code>{{ $page->slug }}</code></p>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">Meta Description</label>
                            <p class="text-muted">{{ $page->meta_description ?: 'Auto-generated from excerpt' }}</p>
                        </div>
                        @if($page->meta_keywords)
                        <div class="col-12">
                            <label class="form-label fw-bold">Meta Keywords</label>
                            <div>
                                @foreach($page->meta_keywords as $keyword)
                                <span class="badge bg-light text-dark me-1">{{ $keyword }}</span>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- SEO Analysis -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">SEO Analysis</h5>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-{{ $seoAnalysis['grade'] === 'A+' || $seoAnalysis['grade'] === 'A' ? 'success' : ($seoAnalysis['grade'] === 'B' || $seoAnalysis['grade'] === 'C' ? 'warning' : 'danger') }} me-2">
                            {{ $seoAnalysis['grade'] }}
                        </span>
                        <span class="text-muted">{{ $seoAnalysis['percentage'] }}%</span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- SEO Score Progress -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="fw-bold">SEO Score</span>
                            <span>{{ $seoAnalysis['score'] }}/100</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-{{ $seoAnalysis['percentage'] >= 80 ? 'success' : ($seoAnalysis['percentage'] >= 60 ? 'warning' : 'danger') }}"
                                 style="width: {{ $seoAnalysis['percentage'] }}%"></div>
                        </div>
                    </div>

                    @if(count($seoAnalysis['issues']) > 0)
                    <!-- SEO Issues -->
                    <div class="mb-4">
                        <h6 class="text-danger mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>Issues Found
                        </h6>
                        <ul class="list-unstyled">
                            @foreach($seoAnalysis['issues'] as $issue)
                            <li class="mb-2">
                                <i class="fas fa-times text-danger me-2"></i>
                                <span class="text-muted">{{ $issue }}</span>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    @if(count($seoAnalysis['recommendations']) > 0)
                    <!-- SEO Recommendations -->
                    <div>
                        <h6 class="text-info mb-3">
                            <i class="fas fa-lightbulb me-2"></i>Recommendations
                        </h6>
                        <ul class="list-unstyled">
                            @foreach($seoAnalysis['recommendations'] as $recommendation)
                            <li class="mb-2">
                                <i class="fas fa-arrow-right text-info me-2"></i>
                                <span class="text-muted">{{ $recommendation }}</span>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Page Statistics -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Page Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-primary mb-1">{{ str_word_count(strip_tags($page->content)) }}</div>
                                <small class="text-muted">Words</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-info mb-1">{{ strlen(strip_tags($page->content)) }}</div>
                                <small class="text-muted">Characters</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success mb-1">{{ substr_count($page->content, '<img') }}</div>
                                <small class="text-muted">Images</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-warning mb-1">{{ substr_count($page->content, '<a') }}</div>
                                <small class="text-muted">Links</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Page Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Page Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Status</label>
                        <div>
                            @if($page->is_published)
                            <span class="badge bg-success">Published</span>
                            @else
                            <span class="badge bg-warning">Draft</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Template</label>
                        <p class="text-muted mb-0">{{ ucfirst($page->template) }}</p>
                    </div>

                    @if($page->published_at)
                    <div class="mb-3">
                        <label class="form-label fw-bold">Published Date</label>
                        <p class="text-muted mb-0">{{ $page->published_at->format('F d, Y \a\t g:i A') }}</p>
                    </div>
                    @endif

                    <div class="mb-3">
                        <label class="form-label fw-bold">Menu Settings</label>
                        <div>
                            @if($page->show_in_menu)
                            <span class="badge bg-info">In Menu (Order: {{ $page->menu_order }})</span>
                            @else
                            <span class="badge bg-secondary">Not in Menu</span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Created</label>
                        <p class="text-muted mb-0">
                            {{ $page->created_at->format('F d, Y \a\t g:i A') }}
                            @if($page->creator)
                            <br><small>by {{ $page->creator->name }}</small>
                            @endif
                        </p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="text-muted mb-0">
                            {{ $page->updated_at->format('F d, Y \a\t g:i A') }}
                            @if($page->updater)
                            <br><small>by {{ $page->updater->name }}</small>
                            @endif
                        </p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.pages.edit', $page) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Page
                        </a>
                        
                        @if($page->is_published)
                        <form method="POST" action="{{ route('admin.pages.update', $page) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_published" value="0">
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-eye-slash me-2"></i>Unpublish
                            </button>
                        </form>
                        @else
                        <form method="POST" action="{{ route('admin.pages.update', $page) }}" style="display: inline;">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="is_published" value="1">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-eye me-2"></i>Publish
                            </button>
                        </form>
                        @endif
                        
                        <a href="{{ route('page.show', $page->slug) }}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-2"></i>View Live
                        </a>
                        
                        <button type="button" class="btn btn-outline-danger" onclick="deletePage()">
                            <i class="fas fa-trash me-2"></i>Delete Page
                        </button>
                    </div>
                </div>
            </div>

            <!-- Page URL -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Page URL</h5>
                </div>
                <div class="card-body">
                    <div class="input-group">
                        <input type="text" class="form-control" value="{{ route('page.show', $page->slug) }}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyUrl()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this page? This action cannot be undone.</p>
                <p><strong>Page:</strong> {{ $page->title }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ route('admin.pages.destroy', $page) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deletePage() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function copyUrl() {
    const urlInput = document.querySelector('input[readonly]');
    urlInput.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('btn-success');
    button.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        button.innerHTML = originalHtml;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}
</script>
@endpush

@push('styles')
<style>
.content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.content h2 {
    font-family: 'Playfair Display', serif;
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content h3 {
    font-family: 'Playfair Display', serif;
    color: #34495e;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.content p {
    margin-bottom: 1.25rem;
}

.content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}
</style>
@endpush
