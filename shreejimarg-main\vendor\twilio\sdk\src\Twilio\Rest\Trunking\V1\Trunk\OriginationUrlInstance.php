<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trunking
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Trunking\V1\Trunk;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $accountSid
 * @property string|null $sid
 * @property string|null $trunkSid
 * @property int $weight
 * @property bool|null $enabled
 * @property string|null $sipUrl
 * @property string|null $friendlyName
 * @property int $priority
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property string|null $url
 */
class OriginationUrlInstance extends InstanceResource
{
    /**
     * Initialize the OriginationUrlInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $trunkSid The SID of the Trunk to associate the resource with.
     * @param string $sid The unique string that we created to identify the OriginationUrl resource to delete.
     */
    public function __construct(Version $version, array $payload, string $trunkSid, ?string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'sid' => Values::array_get($payload, 'sid'),
            'trunkSid' => Values::array_get($payload, 'trunk_sid'),
            'weight' => Values::array_get($payload, 'weight'),
            'enabled' => Values::array_get($payload, 'enabled'),
            'sipUrl' => Values::array_get($payload, 'sip_url'),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'priority' => Values::array_get($payload, 'priority'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['trunkSid' => $trunkSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return OriginationUrlContext Context for this OriginationUrlInstance
     */
    protected function proxy(): OriginationUrlContext
    {
        if (!$this->context) {
            $this->context = new OriginationUrlContext(
                $this->version,
                $this->solution['trunkSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Delete the OriginationUrlInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool
    {

        return $this->proxy()->delete();
    }

    /**
     * Fetch the OriginationUrlInstance
     *
     * @return OriginationUrlInstance Fetched OriginationUrlInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): OriginationUrlInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the OriginationUrlInstance
     *
     * @param array|Options $options Optional Arguments
     * @return OriginationUrlInstance Updated OriginationUrlInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): OriginationUrlInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Trunking.V1.OriginationUrlInstance ' . \implode(' ', $context) . ']';
    }
}

