<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1\Room;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $ttid
 * @property string|null $accountSid
 * @property string|null $roomSid
 * @property string $status
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 * @property \DateTime|null $startTime
 * @property \DateTime|null $endTime
 * @property int|null $duration
 * @property string|null $url
 * @property array|null $configuration
 */
class TranscriptionsInstance extends InstanceResource
{
    /**
     * Initialize the TranscriptionsInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $roomSid The SID of the room new transcriptions resource to be created.
     * @param string $ttid The Twilio type id of the transcriptions resource to fetch.
     */
    public function __construct(Version $version, array $payload, string $roomSid, ?string $ttid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'ttid' => Values::array_get($payload, 'ttid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'roomSid' => Values::array_get($payload, 'room_sid'),
            'status' => Values::array_get($payload, 'status'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'startTime' => Deserialize::dateTime(Values::array_get($payload, 'start_time')),
            'endTime' => Deserialize::dateTime(Values::array_get($payload, 'end_time')),
            'duration' => Values::array_get($payload, 'duration'),
            'url' => Values::array_get($payload, 'url'),
            'configuration' => Values::array_get($payload, 'configuration'),
        ];

        $this->solution = ['roomSid' => $roomSid, 'ttid' => $ttid ?: $this->properties['ttid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return TranscriptionsContext Context for this TranscriptionsInstance
     */
    protected function proxy(): TranscriptionsContext
    {
        if (!$this->context) {
            $this->context = new TranscriptionsContext(
                $this->version,
                $this->solution['roomSid'],
                $this->solution['ttid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the TranscriptionsInstance
     *
     * @return TranscriptionsInstance Fetched TranscriptionsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): TranscriptionsInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Update the TranscriptionsInstance
     *
     * @param array|Options $options Optional Arguments
     * @return TranscriptionsInstance Updated TranscriptionsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): TranscriptionsInstance
    {

        return $this->proxy()->update($options);
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Video.V1.TranscriptionsInstance ' . \implode(' ', $context) . ']';
    }
}

