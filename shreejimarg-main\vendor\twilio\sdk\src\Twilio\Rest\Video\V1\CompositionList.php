<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Video\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Stream;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class CompositionList extends ListResource
    {
    /**
     * Construct the CompositionList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/Compositions';
    }

    /**
     * Create the CompositionInstance
     *
     * @param string $roomSid The SID of the Group Room with the media tracks to be used as composition sources.
     * @param array|Options $options Optional Arguments
     * @return CompositionInstance Created CompositionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $roomSid, array $options = []): CompositionInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'RoomSid' =>
                $roomSid,
            'VideoLayout' =>
                Serialize::jsonObject($options['videoLayout']),
            'AudioSources' =>
                Serialize::map($options['audioSources'], function ($e) { return $e; }),
            'AudioSourcesExcluded' =>
                Serialize::map($options['audioSourcesExcluded'], function ($e) { return $e; }),
            'Resolution' =>
                $options['resolution'],
            'Format' =>
                $options['format'],
            'StatusCallback' =>
                $options['statusCallback'],
            'StatusCallbackMethod' =>
                $options['statusCallbackMethod'],
            'Trim' =>
                Serialize::booleanToString($options['trim']),
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new CompositionInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Reads CompositionInstance records from the API as a list.
     * Unlike stream(), this operation is eager and will load `limit` records into
     * memory before returning.
     *
     * @param array|Options $options Optional Arguments
     * @param int $limit Upper limit for the number of records to return. read()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, read()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return CompositionInstance[] Array of results
     */
    public function read(array $options = [], ?int $limit = null, $pageSize = null): array
    {
        return \iterator_to_array($this->stream($options, $limit, $pageSize), false);
    }

    /**
     * Streams CompositionInstance records from the API as a generator stream.
     * This operation lazily loads records as efficiently as possible until the
     * limit
     * is reached.
     * The results are returned as a generator, so this operation is memory
     * efficient.
     *
     * @param array|Options $options Optional Arguments
     * @param int $limit Upper limit for the number of records to return. stream()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, stream()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return Stream stream of results
     */
    public function stream(array $options = [], ?int $limit = null, $pageSize = null): Stream
    {
        $limits = $this->version->readLimits($limit, $pageSize);

        $page = $this->page($options, $limits['pageSize']);

        return $this->version->stream($page, $limits['limit'], $limits['pageLimit']);
    }

    /**
     * Retrieve a single page of CompositionInstance records from the API.
     * Request is executed immediately
     *
     * @param mixed $pageSize Number of records to return, defaults to 50
     * @param string $pageToken PageToken provided by the API
     * @param mixed $pageNumber Page Number, this value is simply for client state
     * @return CompositionPage Page of CompositionInstance
     */
    public function page(
        array $options = [],
        $pageSize = Values::NONE,
        string $pageToken = Values::NONE,
        $pageNumber = Values::NONE
    ): CompositionPage
    {
        $options = new Values($options);

        $params = Values::of([
            'Status' =>
                $options['status'],
            'DateCreatedAfter' =>
                Serialize::iso8601DateTime($options['dateCreatedAfter']),
            'DateCreatedBefore' =>
                Serialize::iso8601DateTime($options['dateCreatedBefore']),
            'RoomSid' =>
                $options['roomSid'],
            'PageToken' => $pageToken,
            'Page' => $pageNumber,
            'PageSize' => $pageSize,
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json']);
        $response = $this->version->page('GET', $this->uri, $params, [], $headers);

        return new CompositionPage($this->version, $response, $this->solution);
    }

    /**
     * Retrieve a specific page of CompositionInstance records from the API.
     * Request is executed immediately
     *
     * @param string $targetUrl API-generated URL for the requested results page
     * @return CompositionPage Page of CompositionInstance
     */
    public function getPage(string $targetUrl): CompositionPage
    {
        $response = $this->version->getDomain()->getClient()->request(
            'GET',
            $targetUrl
        );

        return new CompositionPage($this->version, $response, $this->solution);
    }


    /**
     * Constructs a CompositionContext
     *
     * @param string $sid The SID of the Composition resource to delete.
     */
    public function getContext(
        string $sid
        
    ): CompositionContext
    {
        return new CompositionContext(
            $this->version,
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Video.V1.CompositionList]';
    }
}
