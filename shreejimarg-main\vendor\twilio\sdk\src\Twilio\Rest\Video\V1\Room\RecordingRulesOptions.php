<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Video\V1\Room;

use Twilio\Options;
use Twilio\Values;

abstract class RecordingRulesOptions
{

    /**
     * @param array $rules A JSON-encoded array of recording rules.
     * @return UpdateRecordingRulesOptions Options builder
     */
    public static function update(
        
        array $rules = Values::ARRAY_NONE

    ): UpdateRecordingRulesOptions
    {
        return new UpdateRecordingRulesOptions(
            $rules
        );
    }

}


class UpdateRecordingRulesOptions extends Options
    {
    /**
     * @param array $rules A JSON-encoded array of recording rules.
     */
    public function __construct(
        
        array $rules = Values::ARRAY_NONE

    ) {
        $this->options['rules'] = $rules;
    }

    /**
     * A JSON-encoded array of recording rules.
     *
     * @param array $rules A JSON-encoded array of recording rules.
     * @return $this Fluent Builder
     */
    public function setRules(array $rules): self
    {
        $this->options['rules'] = $rules;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Video.V1.UpdateRecordingRulesOptions ' . $options . ']';
    }
}

