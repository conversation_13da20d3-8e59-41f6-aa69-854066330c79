<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DebugTwilio extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:twilio';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug Twilio configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Twilio Configuration Debug');
        $this->info('========================');

        $accountSid = config('services.twilio.account_sid');
        $authToken = config('services.twilio.auth_token');
        $fromNumber = config('services.twilio.from_number');

        $this->info('Account SID: ' . ($accountSid ? substr($accountSid, 0, 10) . '...' : 'NOT SET'));
        $this->info('Auth Token: ' . ($authToken ? substr($authToken, 0, 10) . '...' : 'NOT SET'));
        $this->info('From Number: ' . ($fromNumber ?: 'NOT SET'));

        $this->info('');
        $this->info('Environment Variables:');
        $this->info('TWILIO_ACCOUNT_SID: ' . (env('TWILIO_ACCOUNT_SID') ? substr(env('TWILIO_ACCOUNT_SID'), 0, 10) . '...' : 'NOT SET'));
        $this->info('TWILIO_AUTH_TOKEN: ' . (env('TWILIO_AUTH_TOKEN') ? substr(env('TWILIO_AUTH_TOKEN'), 0, 10) . '...' : 'NOT SET'));
        $this->info('TWILIO_FROM_NUMBER: ' . (env('TWILIO_FROM_NUMBER') ?: 'NOT SET'));

        if (!$accountSid || !$authToken) {
            $this->error('');
            $this->error('❌ Twilio credentials are missing!');
            $this->error('Please set the following in your .env file:');
            $this->error('TWILIO_ACCOUNT_SID=your_actual_account_sid');
            $this->error('TWILIO_AUTH_TOKEN=your_actual_auth_token');
            $this->error('TWILIO_FROM_NUMBER=your_twilio_phone_number');
            return 1;
        }

        if ($fromNumber === '+**********') {
            $this->warn('');
            $this->warn('⚠️  Using placeholder phone number!');
            $this->warn('Please update TWILIO_FROM_NUMBER with your actual Twilio phone number');
        }

        $this->info('');
        $this->info('✅ Configuration looks complete');
        return 0;
    }
}
