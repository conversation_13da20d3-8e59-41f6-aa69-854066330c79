<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Trusthub\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Serialize;


class ComplianceRegistrationInquiriesContext extends InstanceContext
    {
    /**
     * Initialize the ComplianceRegistrationInquiriesContext
     *
     * @param Version $version Version that contains the resource
     * @param string $registrationId The unique RegistrationId matching the Regulatory Compliance Inquiry that should be resumed or resubmitted. This value will have been returned by the initial Regulatory Compliance Inquiry creation call.
     */
    public function __construct(
        Version $version,
        $registrationId
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'registrationId' =>
            $registrationId,
        ];

        $this->uri = '/ComplianceInquiries/Registration/' . \rawurlencode($registrationId)
        .'/RegulatoryCompliance/GB/Initialize';
    }

    /**
     * Update the ComplianceRegistrationInquiriesInstance
     *
     * @param array|Options $options Optional Arguments
     * @return ComplianceRegistrationInquiriesInstance Updated ComplianceRegistrationInquiriesInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): ComplianceRegistrationInquiriesInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'IsIsvEmbed' =>
                Serialize::booleanToString($options['isIsvEmbed']),
            'ThemeSetId' =>
                $options['themeSetId'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new ComplianceRegistrationInquiriesInstance(
            $this->version,
            $payload,
            $this->solution['registrationId']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Trusthub.V1.ComplianceRegistrationInquiriesContext ' . \implode(' ', $context) . ']';
    }
}
