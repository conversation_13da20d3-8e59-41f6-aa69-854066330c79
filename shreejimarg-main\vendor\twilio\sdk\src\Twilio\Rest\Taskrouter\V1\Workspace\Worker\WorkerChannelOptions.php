<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace\Worker;

use Twilio\Options;
use Twilio\Values;

abstract class WorkerChannelOptions
{


    /**
     * @param int $capacity The total number of Tasks that the Worker should handle for the TaskChannel type. <PERSON><PERSON><PERSON><PERSON> creates reservations for Tasks of this TaskChannel type up to the specified capacity. If the capacity is 0, no new reservations will be created.
     * @param bool $available Whether the WorkerChannel is available. Set to `false` to prevent the Worker from receiving any new Tasks of this TaskChannel type.
     * @return UpdateWorkerChannelOptions Options builder
     */
    public static function update(
        
        int $capacity = Values::INT_NONE,
        bool $available = Values::BOOL_NONE

    ): UpdateWorkerChannelOptions
    {
        return new UpdateWorkerChannelOptions(
            $capacity,
            $available
        );
    }

}



class UpdateWorkerChannelOptions extends Options
    {
    /**
     * @param int $capacity The total number of Tasks that the Worker should handle for the TaskChannel type. TaskRouter creates reservations for Tasks of this TaskChannel type up to the specified capacity. If the capacity is 0, no new reservations will be created.
     * @param bool $available Whether the WorkerChannel is available. Set to `false` to prevent the Worker from receiving any new Tasks of this TaskChannel type.
     */
    public function __construct(
        
        int $capacity = Values::INT_NONE,
        bool $available = Values::BOOL_NONE

    ) {
        $this->options['capacity'] = $capacity;
        $this->options['available'] = $available;
    }

    /**
     * The total number of Tasks that the Worker should handle for the TaskChannel type. TaskRouter creates reservations for Tasks of this TaskChannel type up to the specified capacity. If the capacity is 0, no new reservations will be created.
     *
     * @param int $capacity The total number of Tasks that the Worker should handle for the TaskChannel type. TaskRouter creates reservations for Tasks of this TaskChannel type up to the specified capacity. If the capacity is 0, no new reservations will be created.
     * @return $this Fluent Builder
     */
    public function setCapacity(int $capacity): self
    {
        $this->options['capacity'] = $capacity;
        return $this;
    }

    /**
     * Whether the WorkerChannel is available. Set to `false` to prevent the Worker from receiving any new Tasks of this TaskChannel type.
     *
     * @param bool $available Whether the WorkerChannel is available. Set to `false` to prevent the Worker from receiving any new Tasks of this TaskChannel type.
     * @return $this Fluent Builder
     */
    public function setAvailable(bool $available): self
    {
        $this->options['available'] = $available;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.UpdateWorkerChannelOptions ' . $options . ']';
    }
}

