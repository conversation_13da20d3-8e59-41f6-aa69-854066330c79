<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', function () {
    return view('welcome');
})->name('home');
Route::get('/foo', function () {
    Artisan::call('storage:link');
    return 'Storage link created successfully! Images should now be visible.';
});
// Test route for debugging
Route::get('/test-routes', function () {
    return response()->json([
        'products' => route('products'),
        'collections' => route('collections'),
        'dashboard' => route('dashboard'),
        'orders' => route('orders'),
        'wishlist' => route('wishlist'),
        'profile' => route('profile'),
        'contact' => route('contact'),
    ]);
})->name('test-routes');

// Debug route to check products and stock status
Route::get('/debug-products', function () {
    $products = \App\Models\Product::with('category')->get();
    $categories = \App\Models\Category::all();

    return response()->json([
        'total_products' => $products->count(),
        'total_categories' => $categories->count(),
        'storage_path_exists' => file_exists(public_path('storage')),
        'storage_link_target' => is_link(public_path('storage')) ? readlink(public_path('storage')) : 'Not a symlink',
        'products' => $products->map(function($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'category' => $product->category->name ?? 'No Category',
                'stock_quantity' => $product->stock_quantity,
                'in_stock' => $product->in_stock,
                'status' => $product->status,
                'is_featured' => $product->is_featured,
                'price' => $product->price,
                'images_raw' => $product->images,
                'images_count' => is_array($product->images) ? count($product->images) : 0,
                'main_image_url' => $product->main_image_url,
                'first_image_path' => $product->images && count($product->images) > 0 ? $product->images[0] : null,
                'first_image_exists' => $product->images && count($product->images) > 0 ? file_exists(storage_path('app/public/' . $product->images[0])) : false,
            ];
        }),
        'categories' => $categories->map(function($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'is_active' => $category->is_active,
                'products_count' => $category->products()->count(),
                'image_raw' => $category->image,
                'image_url' => $category->image_url,
                'image_exists' => $category->image ? file_exists(storage_path('app/public/' . str_replace(['\\/', '\\'], '/', $category->image))) : false,
            ];
        })
    ]);
})->name('debug-products');

// Collections & Products
Route::get('/products', [App\Http\Controllers\ProductController::class, 'index'])->name('products');
Route::get('/collections', [App\Http\Controllers\ProductController::class, 'index'])->name('collections');
Route::get('/collections/{category}', [App\Http\Controllers\ProductController::class, 'category'])->name('collections.category');
Route::get('/product/{slug}', [App\Http\Controllers\ProductController::class, 'show'])->name('product.detail');
Route::get('/search', [App\Http\Controllers\ProductController::class, 'search'])->name('search');
Route::get('/api/products/featured', [App\Http\Controllers\ProductController::class, 'featured'])->name('api.products.featured');

// Shopping Cart & Checkout
Route::get('/cart', [App\Http\Controllers\CartController::class, 'index'])->name('cart');
Route::post('/cart/add', [App\Http\Controllers\CartController::class, 'add'])->name('cart.add');
Route::post('/cart/update', [App\Http\Controllers\CartController::class, 'update'])->name('cart.update');
Route::post('/cart/remove', [App\Http\Controllers\CartController::class, 'remove'])->name('cart.remove');
Route::post('/cart/move-to-wishlist', [App\Http\Controllers\CartController::class, 'moveToWishlist'])->name('cart.move-to-wishlist');
Route::delete('/cart', [App\Http\Controllers\CartController::class, 'clear'])->name('cart.clear');
Route::get('/api/cart/count', [App\Http\Controllers\CartController::class, 'count'])->name('api.cart.count');

// Checkout routes (updated for mobile auth)
Route::get('/checkout', [App\Http\Controllers\OrderController::class, 'checkout'])->name('checkout');
Route::get('/checkout/guest', [App\Http\Controllers\OrderController::class, 'guestCheckout'])->name('checkout.guest');

// Public checkout success page (can be accessed without auth for sharing)
Route::get('/checkout/success/{order}', [App\Http\Controllers\OrderController::class, 'success'])->name('checkout.success');

// Payment Routes (Webhook doesn't need auth)
Route::post('/payment/webhook', [App\Http\Controllers\PaymentController::class, 'webhook'])->name('payment.webhook');
Route::get('/payment/test-details', [App\Http\Controllers\PaymentController::class, 'getTestDetails'])->name('payment.test-details');

// Temporary routes for testing (remove auth requirement)
Route::post('/payment/create-order', [App\Http\Controllers\PaymentController::class, 'createOrder'])->name('payment.create-order.temp');
Route::post('/payment/verify', [App\Http\Controllers\PaymentController::class, 'verifyPayment'])->name('payment.verify.temp');
Route::post('/payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentFailed'])->name('payment.failed.temp');

// Debug route for testing Razorpay service
Route::get('/test-razorpay', function() {
    try {
        $razorpayService = new \App\Services\RazorpayService();

        // Create a test order
        $testOrder = new \App\Models\Order();
        $testOrder->id = 999;
        $testOrder->order_number = 'TEST_' . time();
        $testOrder->total_amount = 100.00;
        $testOrder->currency = 'INR';
        $testOrder->user = (object) [
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ];

        $result = $razorpayService->createOrder($testOrder);

        return response()->json([
            'status' => 'success',
            'razorpay_config' => [
                'key_id' => config('services.razorpay.key_id'),
                'key_secret' => config('services.razorpay.key_secret') ? 'SET' : 'NOT SET',
                'webhook_secret' => config('services.razorpay.webhook_secret') ? 'SET' : 'NOT SET',
            ],
            'result' => $result
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
})->name('test.razorpay');

// Simple payment test route
Route::get('/test-payment-flow', function() {
    try {
        // Create a test user if not exists
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
                'phone' => '9999999999'
            ]
        );

        // Create a test order
        $order = \App\Models\Order::create([
            'order_number' => 'TEST_' . time(),
            'user_id' => $user->id,
            'status' => 'pending',
            'subtotal' => 100.00,
            'tax_amount' => 12.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 112.00,
            'currency' => 'INR',
            'payment_status' => 'pending',
            'payment_method' => 'razorpay',
            'billing_address' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '9999999999',
                'address' => 'Test Address',
                'city' => 'Mumbai',
                'state' => 'maharashtra',
                'pincode' => '400001',
                'country' => 'india'
            ],
            'shipping_address' => [
                'name' => 'Test User',
                'address' => 'Test Address',
                'city' => 'Mumbai',
                'state' => 'maharashtra',
                'pincode' => '400001',
                'country' => 'india'
            ]
        ]);

        // Test Razorpay order creation
        $razorpayService = new \App\Services\RazorpayService();
        $result = $razorpayService->createOrder($order);

        return response()->json([
            'status' => 'success',
            'order_id' => $order->id,
            'razorpay_result' => $result,
            'test_url' => route('test.payment.page', $order->id)
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
})->name('test.payment.flow');

// Test payment page
Route::get('/test-payment/{order}', function($orderId) {
    $order = \App\Models\Order::findOrFail($orderId);
    return view('test-payment', compact('order'));
})->name('test.payment.page');

// Mobile Authentication (Primary)
Route::get('/login', [App\Http\Controllers\MobileAuthController::class, 'showMobileLogin'])->name('login');
Route::post('/mobile/send-otp', [App\Http\Controllers\MobileAuthController::class, 'sendOtp'])->name('mobile.send-otp');
Route::get('/mobile/verify', [App\Http\Controllers\MobileAuthController::class, 'showOtpVerification'])->name('mobile.verify');
Route::post('/mobile/verify-otp', [App\Http\Controllers\MobileAuthController::class, 'verifyOtp'])->name('mobile.verify-otp');
Route::post('/mobile/resend-otp', [App\Http\Controllers\MobileAuthController::class, 'resendOtp'])->name('mobile.resend-otp');
Route::post('/logout', [App\Http\Controllers\MobileAuthController::class, 'logout'])->name('logout');
Route::get('/auth/check', [App\Http\Controllers\MobileAuthController::class, 'checkAuth'])->name('auth.check');

// Admin Authentication (Email-based for admin users only)
Route::prefix('admin')->group(function () {
    Route::get('/login', [App\Http\Controllers\AuthController::class, 'showLogin'])->name('admin.login');
    Route::post('/login', [App\Http\Controllers\AuthController::class, 'login'])->name('admin.login.post');
});

// Registration (for users who want email accounts)
Route::get('/register', [App\Http\Controllers\AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [App\Http\Controllers\AuthController::class, 'register'])->name('register.post');

// User Dashboard & Profile
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\UserController::class, 'dashboard'])->name('dashboard');
    Route::get('/profile', [App\Http\Controllers\UserController::class, 'profile'])->name('profile');
    Route::put('/profile', [App\Http\Controllers\UserController::class, 'updateProfile'])->name('profile.update');
    Route::put('/profile/password', [App\Http\Controllers\UserController::class, 'updatePassword'])->name('profile.password');
    Route::put('/profile/preferences', [App\Http\Controllers\UserController::class, 'updatePreferences'])->name('profile.preferences');
    Route::delete('/profile', [App\Http\Controllers\UserController::class, 'deleteAccount'])->name('profile.delete');

    // Orders
    Route::get('/orders', [App\Http\Controllers\OrderController::class, 'index'])->name('orders');
    Route::get('/orders/{id}', [App\Http\Controllers\OrderController::class, 'show'])->name('order.detail');
    Route::put('/orders/{id}/cancel', [App\Http\Controllers\OrderController::class, 'cancel'])->name('order.cancel');

    // Wishlist
    Route::get('/wishlist', [App\Http\Controllers\WishlistController::class, 'index'])->name('wishlist');
    Route::post('/wishlist/toggle', [App\Http\Controllers\WishlistController::class, 'toggle'])->name('wishlist.toggle');
    Route::post('/wishlist/add', [App\Http\Controllers\WishlistController::class, 'add'])->name('wishlist.add');
    Route::delete('/wishlist/{id}', [App\Http\Controllers\WishlistController::class, 'remove'])->name('wishlist.remove');
    Route::delete('/wishlist', [App\Http\Controllers\WishlistController::class, 'clear'])->name('wishlist.clear');
    Route::post('/api/wishlist/check', [App\Http\Controllers\WishlistController::class, 'check'])->name('api.wishlist.check');

    // Checkout & Orders (Authenticated)
    Route::post('/checkout', [App\Http\Controllers\OrderController::class, 'store'])->name('checkout.store');

    // Password setup for mobile-only users
    Route::get('/account/set-password', function() {
        $user = Auth::user();
        if (!$user || !$user->canSetPassword()) {
            return redirect()->route('dashboard')->with('error', 'You are not eligible to set a password.');
        }
        return view('auth.set-password');
    })->name('account.set-password.form');
    Route::post('/account/set-password', [App\Http\Controllers\MobileAuthController::class, 'setPassword'])->name('account.set-password');

    // Payment Routes (Authenticated)
    Route::post('/payment/create-order', [App\Http\Controllers\PaymentController::class, 'createOrder'])->name('payment.create-order');
    Route::post('/payment/verify', [App\Http\Controllers\PaymentController::class, 'verifyPayment'])->name('payment.verify');
    Route::post('/payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentFailed'])->name('payment.failed');
});

// Static Pages - Keep existing routes for backward compatibility
Route::get('/about', function () {
    return view('pages.about');
})->name('about');

Route::get('/contact', function () {
    return view('pages.contact');
})->name('contact');

// Customer Service Pages - Keep existing routes for backward compatibility
Route::get('/shipping', function () {
    return view('pages.shipping');
})->name('shipping');

Route::get('/returns', function () {
    return view('pages.returns');
})->name('returns');

Route::get('/size-guide', function () {
    return view('pages.size-guide');
})->name('size-guide');

Route::get('/jewelry-care', function () {
    return view('pages.jewelry-care');
})->name('jewelry-care');

Route::get('/warranty', function () {
    return view('pages.warranty');
})->name('warranty');

// Legal Pages - Keep existing routes for backward compatibility
Route::get('/privacy-policy', function () {
    return view('pages.privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('pages.terms-of-service');
})->name('terms-of-service');

Route::get('/cookie-policy', function () {
    return view('pages.cookie-policy');
})->name('cookie-policy');

// Dynamic Pages System
Route::get('/page/{slug}', [App\Http\Controllers\PageController::class, 'show'])->name('page.show');
Route::get('/pages/search', [App\Http\Controllers\PageController::class, 'search'])->name('pages.search');

// Admin Routes (for product management)
Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');

    // Product Management
    Route::get('/products', [App\Http\Controllers\Admin\AdminController::class, 'products'])->name('products.index');
    Route::get('/products/create', [App\Http\Controllers\Admin\AdminController::class, 'createProduct'])->name('products.create');
    Route::post('/products', [App\Http\Controllers\Admin\AdminController::class, 'storeProduct'])->name('products.store');
    Route::get('/products/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editProduct'])->name('products.edit');
    Route::put('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateProduct'])->name('products.update');
    Route::delete('/products/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteProduct'])->name('products.delete');
    Route::post('/products/bulk-delete', [App\Http\Controllers\Admin\AdminController::class, 'bulkDeleteProducts'])->name('products.bulk-delete');


    // Category Management
    Route::get('/categories', [App\Http\Controllers\Admin\AdminController::class, 'categories'])->name('categories.index');
    Route::get('/categories/create', [App\Http\Controllers\Admin\AdminController::class, 'createCategory'])->name('categories.create');
    Route::post('/categories', [App\Http\Controllers\Admin\AdminController::class, 'storeCategory'])->name('categories.store');
    Route::get('/categories/{id}/edit', [App\Http\Controllers\Admin\AdminController::class, 'editCategory'])->name('categories.edit');
    Route::put('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'updateCategory'])->name('categories.update');
    Route::delete('/categories/{id}', [App\Http\Controllers\Admin\AdminController::class, 'deleteCategory'])->name('categories.delete');

    // Order Management
    Route::get('/orders', function () {
        return view('admin.orders.index');
    })->name('orders.index');

    // Order Tracking Routes
    Route::prefix('orders/tracking')->name('orders.tracking.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\OrderTrackingController::class, 'index'])->name('index');
        Route::get('/{id}', [App\Http\Controllers\Admin\OrderTrackingController::class, 'show'])->name('show');
        Route::get('/{id}/info', [App\Http\Controllers\Admin\OrderTrackingController::class, 'getTrackingInfo'])->name('info');
        Route::post('/{id}/status', [App\Http\Controllers\Admin\OrderTrackingController::class, 'updateStatus'])->name('update-status');
        Route::post('/{id}/tracking', [App\Http\Controllers\Admin\OrderTrackingController::class, 'addTracking'])->name('add-tracking');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\OrderTrackingController::class, 'bulkUpdateStatus'])->name('bulk-update');
        Route::get('/export', [App\Http\Controllers\Admin\OrderTrackingController::class, 'exportOrders'])->name('export');
    });

    // Page Management
    Route::resource('pages', App\Http\Controllers\Admin\PageController::class);
    Route::post('/pages/bulk-action', [App\Http\Controllers\Admin\PageController::class, 'bulkAction'])->name('pages.bulk-action');

    // Customer Management
    Route::get('/customers', function () {
        return view('admin.customers.index');
    })->name('customers.index');

    // Admin Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
        Route::put('/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('password');
        Route::put('/preferences', [App\Http\Controllers\Admin\ProfileController::class, 'updatePreferences'])->name('preferences');
        Route::get('/activity', [App\Http\Controllers\Admin\ProfileController::class, 'activity'])->name('activity');
    });
});
