@extends('layouts.app')

@section('title', 'Set Password - ShreeJi Jewelry')
@section('description', 'Set a password for your account to enable email login and enhanced security.')

@section('content')
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <h2 class="font-playfair text-primary-pink mb-2">
                                <i class="fas fa-gem me-2"></i>ShreeJi
                            </h2>
                            <h4 class="font-playfair mb-3">Complete Your Account</h4>
                            <p class="text-muted">Set a password to enable email login and secure your account</p>
                        </div>

                        <!-- Benefits Section -->
                        <div class="alert alert-info mb-4">
                            <h6 class="mb-2"><i class="fas fa-star text-warning me-2"></i>Benefits of Setting a Password:</h6>
                            <ul class="mb-0 small">
                                <li>Login with email or mobile number</li>
                                <li>Enhanced account security</li>
                                <li>Faster checkout for future orders</li>
                                <li>Access to exclusive member benefits</li>
                            </ul>
                        </div>
                        
                        <!-- Password Setup Form -->
                        <form id="setPasswordForm">
                            @csrf
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control form-control-lg" 
                                       id="email" name="email" required 
                                       placeholder="Enter your email address">
                                <div class="invalid-feedback" id="emailError"></div>
                                <small class="text-muted">We'll use this for login and order updates</small>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" 
                                           id="password" name="password" required 
                                           placeholder="Create a strong password" minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="passwordError"></div>
                            </div>

                            <div class="mb-4">
                                <label for="password_confirmation" class="form-label">Confirm Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" 
                                           id="password_confirmation" name="password_confirmation" required 
                                           placeholder="Confirm your password" minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                                        <i class="fas fa-eye" id="passwordConfirmationToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="passwordConfirmationError"></div>
                            </div>

                            <button type="submit" class="btn btn-primary-pink w-100 btn-lg mb-3" id="setPasswordBtn">
                                <i class="fas fa-shield-alt me-2"></i>Set Password & Complete Account
                            </button>
                        </form>
                        
                        <!-- Skip Option -->
                        <div class="text-center mb-4">
                            <p class="text-muted mb-3">You can also set this up later</p>
                            <button class="btn btn-outline-secondary" onclick="skipPasswordSetup()">
                                <i class="fas fa-arrow-right me-2"></i>Skip for Now
                            </button>
                        </div>
                        
                        <!-- Current Account Info -->
                        <div class="text-center border-top pt-4">
                            <small class="text-muted">
                                <i class="fas fa-mobile-alt me-2"></i>
                                Currently logged in with: <strong>{{ auth()->user()->formatted_phone }}</strong>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Password visibility toggle
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + 'ToggleIcon');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Password confirmation validation
    document.getElementById('password_confirmation').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmation = this.value;
        
        if (confirmation && password !== confirmation) {
            showError('password_confirmation', 'Passwords do not match');
        } else {
            clearError('password_confirmation');
        }
    });

    // Handle form submission
    document.getElementById('setPasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const setPasswordBtn = document.getElementById('setPasswordBtn');

        // Validate passwords match
        const password = document.getElementById('password').value;
        const confirmation = document.getElementById('password_confirmation').value;
        
        if (password !== confirmation) {
            showError('password_confirmation', 'Passwords do not match');
            return;
        }

        // Clear previous errors
        clearErrors();

        // Show loading state
        setPasswordBtn.disabled = true;
        setPasswordBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Setting Password...';

        fetch('{{ route("account.set-password") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showSuccess(data.message);
                
                // Redirect after short delay
                setTimeout(() => {
                    window.location.href = '{{ route("dashboard") }}';
                }, 2000);
            } else {
                if (data.errors) {
                    // Show field-specific errors
                    Object.keys(data.errors).forEach(field => {
                        showError(field, data.errors[field][0]);
                    });
                } else {
                    showError('email', data.message);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('email', 'Failed to set password. Please try again.');
        })
        .finally(() => {
            // Reset button state
            setPasswordBtn.disabled = false;
            setPasswordBtn.innerHTML = '<i class="fas fa-shield-alt me-2"></i>Set Password & Complete Account';
        });
    });

    function skipPasswordSetup() {
        if (confirm('Are you sure you want to skip password setup? You can set it up later from your account settings.')) {
            window.location.href = '{{ route("dashboard") }}';
        }
    }

    function showError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + 'Error');
        
        field.classList.add('is-invalid');
        if (errorDiv) {
            errorDiv.textContent = message;
        }
    }

    function clearError(fieldId) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + 'Error');
        
        field.classList.remove('is-invalid');
        if (errorDiv) {
            errorDiv.textContent = '';
        }
    }

    function clearErrors() {
        const fields = ['email', 'password', 'password_confirmation'];
        fields.forEach(fieldId => {
            clearError(fieldId);
        });
        
        // Remove success message
        const successDiv = document.getElementById('successMessage');
        if (successDiv) {
            successDiv.remove();
        }
    }

    function showSuccess(message) {
        // Create or update success message
        let successDiv = document.getElementById('successMessage');
        if (!successDiv) {
            successDiv = document.createElement('div');
            successDiv.id = 'successMessage';
            successDiv.className = 'alert alert-success mt-3';
            document.querySelector('.card-body').appendChild(successDiv);
        }
        successDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + message;
    }
</script>
@endpush
