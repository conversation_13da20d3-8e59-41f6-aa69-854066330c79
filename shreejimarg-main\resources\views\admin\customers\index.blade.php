@extends('layouts.admin')

@section('title', 'Manage Customers - Admin - ShreeJi Jewelry')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Customer Management</h1>
        <p class="mb-0 text-muted">Manage customer accounts and relationships</p>
    </div>
    <div>
        <button class="btn btn-primary-pink">
            <i class="fas fa-user-plus me-2"></i>Add Customer
        </button>
    </div>
</div>

<!-- Quick Stats -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">1,234</h4>
                        <p class="text-muted mb-0">Total Customers</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">89</h4>
                        <p class="text-muted mb-0">VIP Members</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">23</h4>
                        <p class="text-muted mb-0">New This Month</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">567</h4>
                        <p class="text-muted mb-0">Active Buyers</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-center">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Search customers...">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">All Members</option>
                    <option value="regular">Regular</option>
                    <option value="premium">Premium</option>
                    <option value="vip">VIP</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">Sort by</option>
                    <option value="name">Name</option>
                    <option value="email">Email</option>
                    <option value="spent">Total Spent</option>
                    <option value="joined">Date Joined</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-1"></i>Filter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customers Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Customers</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>Export
            </button>
            <button class="btn btn-outline-primary btn-sm">
                <i class="fas fa-sync me-1"></i>Refresh
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Customer</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Membership</th>
                        <th>Total Spent</th>
                        <th>Orders</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary-pink text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                     style="width: 40px; height: 40px;">
                                    P
                                </div>
                                <div>
                                    <h6 class="mb-0">Priya Sharma</h6>
                                    <small class="text-muted">Customer ID: #C001</small>
                                </div>
                            </div>
                        </td>
                        <td><EMAIL></td>
                        <td>+91 98765 43210</td>
                        <td><span class="badge bg-warning">VIP</span></td>
                        <td><strong>₹2,45,000</strong></td>
                        <td>15</td>
                        <td>Dec 15, 2023</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-primary btn-sm" title="View Profile">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-success btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" title="Orders">
                                    <i class="fas fa-shopping-bag"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <!-- Add more sample rows here -->
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-info-circle text-muted me-2"></i>
                            <span class="text-muted">This is a placeholder view. Real customer data will be displayed here.</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="text-muted mb-0">Showing 1 to 10 of 1,234 customers</p>
            </div>
            <div class="col-md-6">
                <nav aria-label="Customers pagination">
                    <ul class="pagination justify-content-end mb-0">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
@endsection
