<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Trusthub\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;


class ComplianceInquiriesList extends ListResource
    {
    /**
     * Construct the ComplianceInquiriesList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];

        $this->uri = '/ComplianceInquiries/Customers/Initialize';
    }

    /**
     * Create the ComplianceInquiriesInstance
     *
     * @param string $primaryProfileSid The unique SID identifier of the Primary Customer Profile that should be used as a parent. Only necessary when creating a secondary Customer Profile.
     * @param array|Options $options Optional Arguments
     * @return ComplianceInquiriesInstance Created ComplianceInquiriesInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $primaryProfileSid, array $options = []): ComplianceInquiriesInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'PrimaryProfileSid' =>
                $primaryProfileSid,
            'NotificationEmail' =>
                $options['notificationEmail'],
            'ThemeSetId' =>
                $options['themeSetId'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new ComplianceInquiriesInstance(
            $this->version,
            $payload
        );
    }


    /**
     * Constructs a ComplianceInquiriesContext
     *
     * @param string $customerId The unique CustomerId matching the Customer Profile/Compliance Inquiry that should be resumed or resubmitted. This value will have been returned by the initial Compliance Inquiry creation call.
     */
    public function getContext(
        string $customerId
        
    ): ComplianceInquiriesContext
    {
        return new ComplianceInquiriesContext(
            $this->version,
            $customerId
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Trusthub.V1.ComplianceInquiriesList]';
    }
}
