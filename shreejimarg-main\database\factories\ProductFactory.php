<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        $price = $this->faker->randomFloat(2, 50, 5000);
        $salePrice = $this->faker->boolean(30) ? $this->faker->randomFloat(2, 30, $price - 10) : null;

        return [
            'category_id' => Category::factory(),
            'name' => ucwords($name),
            'slug' => Str::slug($name) . '-' . $this->faker->unique()->numberBetween(1000, 9999),
            'sku' => 'SKU-' . $this->faker->unique()->numerify('######'),
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->sentence(10),
            'price' => $price,
            'sale_price' => $salePrice,
            'stock_quantity' => $this->faker->numberBetween(0, 100),
            'manage_stock' => $this->faker->boolean(80),
            'in_stock' => $this->faker->boolean(90),
            'is_featured' => $this->faker->boolean(20),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft']),
            'images' => [
                'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                'https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
            ],
            'specifications' => [
                'Material' => $this->faker->randomElement(['Gold', 'Silver', 'Platinum']),
                'Weight' => $this->faker->randomFloat(2, 1, 50) . 'g',
                'Dimensions' => $this->faker->randomFloat(1, 10, 30) . 'mm',
            ],
            'sizes' => $this->faker->randomElements(['XS', 'S', 'M', 'L', 'XL'], $this->faker->numberBetween(1, 3)),
            'weight' => $this->faker->randomFloat(2, 1, 50),
            'metal_type' => $this->faker->randomElement(['Gold', 'Silver', 'Platinum', 'Sterling Silver']),
            'metal_purity' => $this->faker->randomElement(['18K', '22K', '24K', '925', 'PT950']),
            'stone_type' => $this->faker->optional()->randomElement(['Diamond', 'Ruby', 'Emerald', 'Sapphire', 'Pearl']),
            'stone_weight' => $this->faker->optional()->randomFloat(2, 0.1, 5),
            'certification' => $this->faker->optional()->randomElement(['GIA', 'IGI', 'GJEPC', 'BIS']),
            'sort_order' => $this->faker->numberBetween(0, 100),
        ];
    }

    /**
     * Indicate that the product is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the product is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the product is in stock.
     */
    public function inStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'in_stock' => true,
            'stock_quantity' => $this->faker->numberBetween(1, 100),
        ]);
    }

    /**
     * Indicate that the product is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'in_stock' => false,
            'stock_quantity' => 0,
        ]);
    }

    /**
     * Indicate that the product is on sale.
     */
    public function onSale(): static
    {
        return $this->state(function (array $attributes) {
            $price = $attributes['price'] ?? $this->faker->randomFloat(2, 100, 5000);
            return [
                'price' => $price,
                'sale_price' => $this->faker->randomFloat(2, 50, $price - 10),
            ];
        });
    }

    /**
     * Create a simple product for testing.
     */
    public function simple(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Test Product',
            'price' => 100.00,
            'stock_quantity' => 10,
            'in_stock' => true,
            'status' => 'active',
            'manage_stock' => true,
        ]);
    }
}
