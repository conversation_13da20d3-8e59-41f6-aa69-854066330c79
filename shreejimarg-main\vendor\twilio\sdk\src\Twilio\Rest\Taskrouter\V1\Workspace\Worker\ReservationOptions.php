<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace\Worker;

use Twilio\Options;
use Twilio\Values;

abstract class ReservationOptions
{

    /**
     * @param string $reservationStatus Returns the list of reservations for a worker with a specified ReservationStatus. Can be: `pending`, `accepted`, `rejected`, `timeout`, `canceled`, or `rescinded`.
     * @return ReadReservationOptions Options builder
     */
    public static function read(
        
        string $reservationStatus = Values::NONE

    ): ReadReservationOptions
    {
        return new ReadReservationOptions(
            $reservationStatus
        );
    }

    /**
     * @param string $reservationStatus
     * @param string $workerActivitySid The new worker activity SID if rejecting a reservation.
     * @param string $instruction The assignment instruction for the reservation.
     * @param string $dequeuePostWorkActivitySid The SID of the Activity resource to start after executing a Dequeue instruction.
     * @param string $dequeueFrom The caller ID of the call to the worker when executing a Dequeue instruction.
     * @param string $dequeueRecord Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
     * @param int $dequeueTimeout The timeout for call when executing a Dequeue instruction.
     * @param string $dequeueTo The contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @param string $dequeueStatusCallbackUrl The callback URL for completed call event when executing a Dequeue instruction.
     * @param string $callFrom The Caller ID of the outbound call when executing a Call instruction.
     * @param string $callRecord Whether to record both legs of a call when executing a Call instruction.
     * @param int $callTimeout The timeout for a call when executing a Call instruction.
     * @param string $callTo The contact URI of the worker when executing a Call instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @param string $callUrl TwiML URI executed on answering the worker's leg as a result of the Call instruction.
     * @param string $callStatusCallbackUrl The URL to call for the completed call event when executing a Call instruction.
     * @param bool $callAccept Whether to accept a reservation when executing a Call instruction.
     * @param string $redirectCallSid The Call SID of the call parked in the queue when executing a Redirect instruction.
     * @param bool $redirectAccept Whether the reservation should be accepted when executing a Redirect instruction.
     * @param string $redirectUrl TwiML URI to redirect the call to when executing the Redirect instruction.
     * @param string $to The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @param string $from The caller ID of the call to the worker when executing a Conference instruction.
     * @param string $statusCallback The URL we should call using the `status_callback_method` to send status information to your application.
     * @param string $statusCallbackMethod The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
     * @param string $statusCallbackEvent The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
     * @param int $timeout The timeout for a call when executing a Conference instruction.
     * @param bool $record Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     * @param bool $muted Whether the agent is muted in the conference. Defaults to `false`.
     * @param string $beep Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     * @param bool $startConferenceOnEnter Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     * @param bool $endConferenceOnExit Whether to end the conference when the agent leaves.
     * @param string $waitUrl The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @param bool $earlyMedia Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
     * @param int $maxParticipants The maximum number of participants allowed in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     * @param string $conferenceStatusCallback The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     * @param string $conferenceStatusCallbackMethod The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $conferenceStatusCallbackEvent The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
     * @param string $conferenceRecord Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     * @param string $conferenceTrim Whether to trim leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     * @param string $recordingChannels The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     * @param string $recordingStatusCallback The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     * @param string $recordingStatusCallbackMethod The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $conferenceRecordingStatusCallback The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $region The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     * @param string $sipAuthUsername The SIP username used for authentication.
     * @param string $sipAuthPassword The SIP password for authentication.
     * @param string[] $dequeueStatusCallbackEvent The call progress events sent via webhooks as a result of a Dequeue instruction.
     * @param string $postWorkActivitySid The new worker activity SID after executing a Conference instruction.
     * @param bool $endConferenceOnCustomerExit Whether to end the conference when the customer leaves.
     * @param bool $beepOnCustomerEntrance Whether to play a notification beep when the customer joins.
     * @param string $jitterBufferSize The jitter buffer size for conference. Can be: `small`, `medium`, `large`, `off`.
     * @param string $ifMatch The If-Match HTTP request header
     * @return UpdateReservationOptions Options builder
     */
    public static function update(
        
        string $reservationStatus = Values::NONE,
        string $workerActivitySid = Values::NONE,
        string $instruction = Values::NONE,
        string $dequeuePostWorkActivitySid = Values::NONE,
        string $dequeueFrom = Values::NONE,
        string $dequeueRecord = Values::NONE,
        int $dequeueTimeout = Values::INT_NONE,
        string $dequeueTo = Values::NONE,
        string $dequeueStatusCallbackUrl = Values::NONE,
        string $callFrom = Values::NONE,
        string $callRecord = Values::NONE,
        int $callTimeout = Values::INT_NONE,
        string $callTo = Values::NONE,
        string $callUrl = Values::NONE,
        string $callStatusCallbackUrl = Values::NONE,
        bool $callAccept = Values::BOOL_NONE,
        string $redirectCallSid = Values::NONE,
        bool $redirectAccept = Values::BOOL_NONE,
        string $redirectUrl = Values::NONE,
        string $to = Values::NONE,
        string $from = Values::NONE,
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        array $statusCallbackEvent = Values::ARRAY_NONE,
        int $timeout = Values::INT_NONE,
        bool $record = Values::BOOL_NONE,
        bool $muted = Values::BOOL_NONE,
        string $beep = Values::NONE,
        bool $startConferenceOnEnter = Values::BOOL_NONE,
        bool $endConferenceOnExit = Values::BOOL_NONE,
        string $waitUrl = Values::NONE,
        string $waitMethod = Values::NONE,
        bool $earlyMedia = Values::BOOL_NONE,
        int $maxParticipants = Values::INT_NONE,
        string $conferenceStatusCallback = Values::NONE,
        string $conferenceStatusCallbackMethod = Values::NONE,
        array $conferenceStatusCallbackEvent = Values::ARRAY_NONE,
        string $conferenceRecord = Values::NONE,
        string $conferenceTrim = Values::NONE,
        string $recordingChannels = Values::NONE,
        string $recordingStatusCallback = Values::NONE,
        string $recordingStatusCallbackMethod = Values::NONE,
        string $conferenceRecordingStatusCallback = Values::NONE,
        string $conferenceRecordingStatusCallbackMethod = Values::NONE,
        string $region = Values::NONE,
        string $sipAuthUsername = Values::NONE,
        string $sipAuthPassword = Values::NONE,
        array $dequeueStatusCallbackEvent = Values::ARRAY_NONE,
        string $postWorkActivitySid = Values::NONE,
        bool $endConferenceOnCustomerExit = Values::BOOL_NONE,
        bool $beepOnCustomerEntrance = Values::BOOL_NONE,
        string $jitterBufferSize = Values::NONE,
        string $ifMatch = Values::NONE

    ): UpdateReservationOptions
    {
        return new UpdateReservationOptions(
            $reservationStatus,
            $workerActivitySid,
            $instruction,
            $dequeuePostWorkActivitySid,
            $dequeueFrom,
            $dequeueRecord,
            $dequeueTimeout,
            $dequeueTo,
            $dequeueStatusCallbackUrl,
            $callFrom,
            $callRecord,
            $callTimeout,
            $callTo,
            $callUrl,
            $callStatusCallbackUrl,
            $callAccept,
            $redirectCallSid,
            $redirectAccept,
            $redirectUrl,
            $to,
            $from,
            $statusCallback,
            $statusCallbackMethod,
            $statusCallbackEvent,
            $timeout,
            $record,
            $muted,
            $beep,
            $startConferenceOnEnter,
            $endConferenceOnExit,
            $waitUrl,
            $waitMethod,
            $earlyMedia,
            $maxParticipants,
            $conferenceStatusCallback,
            $conferenceStatusCallbackMethod,
            $conferenceStatusCallbackEvent,
            $conferenceRecord,
            $conferenceTrim,
            $recordingChannels,
            $recordingStatusCallback,
            $recordingStatusCallbackMethod,
            $conferenceRecordingStatusCallback,
            $conferenceRecordingStatusCallbackMethod,
            $region,
            $sipAuthUsername,
            $sipAuthPassword,
            $dequeueStatusCallbackEvent,
            $postWorkActivitySid,
            $endConferenceOnCustomerExit,
            $beepOnCustomerEntrance,
            $jitterBufferSize,
            $ifMatch
        );
    }

}


class ReadReservationOptions extends Options
    {
    /**
     * @param string $reservationStatus Returns the list of reservations for a worker with a specified ReservationStatus. Can be: `pending`, `accepted`, `rejected`, `timeout`, `canceled`, or `rescinded`.
     */
    public function __construct(
        
        string $reservationStatus = Values::NONE

    ) {
        $this->options['reservationStatus'] = $reservationStatus;
    }

    /**
     * Returns the list of reservations for a worker with a specified ReservationStatus. Can be: `pending`, `accepted`, `rejected`, `timeout`, `canceled`, or `rescinded`.
     *
     * @param string $reservationStatus Returns the list of reservations for a worker with a specified ReservationStatus. Can be: `pending`, `accepted`, `rejected`, `timeout`, `canceled`, or `rescinded`.
     * @return $this Fluent Builder
     */
    public function setReservationStatus(string $reservationStatus): self
    {
        $this->options['reservationStatus'] = $reservationStatus;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.ReadReservationOptions ' . $options . ']';
    }
}

class UpdateReservationOptions extends Options
    {
    /**
     * @param string $reservationStatus
     * @param string $workerActivitySid The new worker activity SID if rejecting a reservation.
     * @param string $instruction The assignment instruction for the reservation.
     * @param string $dequeuePostWorkActivitySid The SID of the Activity resource to start after executing a Dequeue instruction.
     * @param string $dequeueFrom The caller ID of the call to the worker when executing a Dequeue instruction.
     * @param string $dequeueRecord Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
     * @param int $dequeueTimeout The timeout for call when executing a Dequeue instruction.
     * @param string $dequeueTo The contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @param string $dequeueStatusCallbackUrl The callback URL for completed call event when executing a Dequeue instruction.
     * @param string $callFrom The Caller ID of the outbound call when executing a Call instruction.
     * @param string $callRecord Whether to record both legs of a call when executing a Call instruction.
     * @param int $callTimeout The timeout for a call when executing a Call instruction.
     * @param string $callTo The contact URI of the worker when executing a Call instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @param string $callUrl TwiML URI executed on answering the worker's leg as a result of the Call instruction.
     * @param string $callStatusCallbackUrl The URL to call for the completed call event when executing a Call instruction.
     * @param bool $callAccept Whether to accept a reservation when executing a Call instruction.
     * @param string $redirectCallSid The Call SID of the call parked in the queue when executing a Redirect instruction.
     * @param bool $redirectAccept Whether the reservation should be accepted when executing a Redirect instruction.
     * @param string $redirectUrl TwiML URI to redirect the call to when executing the Redirect instruction.
     * @param string $to The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @param string $from The caller ID of the call to the worker when executing a Conference instruction.
     * @param string $statusCallback The URL we should call using the `status_callback_method` to send status information to your application.
     * @param string $statusCallbackMethod The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
     * @param string $statusCallbackEvent The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
     * @param int $timeout The timeout for a call when executing a Conference instruction.
     * @param bool $record Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     * @param bool $muted Whether the agent is muted in the conference. Defaults to `false`.
     * @param string $beep Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     * @param bool $startConferenceOnEnter Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     * @param bool $endConferenceOnExit Whether to end the conference when the agent leaves.
     * @param string $waitUrl The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @param bool $earlyMedia Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
     * @param int $maxParticipants The maximum number of participants allowed in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     * @param string $conferenceStatusCallback The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     * @param string $conferenceStatusCallbackMethod The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $conferenceStatusCallbackEvent The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
     * @param string $conferenceRecord Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     * @param string $conferenceTrim Whether to trim leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     * @param string $recordingChannels The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     * @param string $recordingStatusCallback The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     * @param string $recordingStatusCallbackMethod The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $conferenceRecordingStatusCallback The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $region The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     * @param string $sipAuthUsername The SIP username used for authentication.
     * @param string $sipAuthPassword The SIP password for authentication.
     * @param string[] $dequeueStatusCallbackEvent The call progress events sent via webhooks as a result of a Dequeue instruction.
     * @param string $postWorkActivitySid The new worker activity SID after executing a Conference instruction.
     * @param bool $endConferenceOnCustomerExit Whether to end the conference when the customer leaves.
     * @param bool $beepOnCustomerEntrance Whether to play a notification beep when the customer joins.
     * @param string $jitterBufferSize The jitter buffer size for conference. Can be: `small`, `medium`, `large`, `off`.
     * @param string $ifMatch The If-Match HTTP request header
     */
    public function __construct(
        
        string $reservationStatus = Values::NONE,
        string $workerActivitySid = Values::NONE,
        string $instruction = Values::NONE,
        string $dequeuePostWorkActivitySid = Values::NONE,
        string $dequeueFrom = Values::NONE,
        string $dequeueRecord = Values::NONE,
        int $dequeueTimeout = Values::INT_NONE,
        string $dequeueTo = Values::NONE,
        string $dequeueStatusCallbackUrl = Values::NONE,
        string $callFrom = Values::NONE,
        string $callRecord = Values::NONE,
        int $callTimeout = Values::INT_NONE,
        string $callTo = Values::NONE,
        string $callUrl = Values::NONE,
        string $callStatusCallbackUrl = Values::NONE,
        bool $callAccept = Values::BOOL_NONE,
        string $redirectCallSid = Values::NONE,
        bool $redirectAccept = Values::BOOL_NONE,
        string $redirectUrl = Values::NONE,
        string $to = Values::NONE,
        string $from = Values::NONE,
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        array $statusCallbackEvent = Values::ARRAY_NONE,
        int $timeout = Values::INT_NONE,
        bool $record = Values::BOOL_NONE,
        bool $muted = Values::BOOL_NONE,
        string $beep = Values::NONE,
        bool $startConferenceOnEnter = Values::BOOL_NONE,
        bool $endConferenceOnExit = Values::BOOL_NONE,
        string $waitUrl = Values::NONE,
        string $waitMethod = Values::NONE,
        bool $earlyMedia = Values::BOOL_NONE,
        int $maxParticipants = Values::INT_NONE,
        string $conferenceStatusCallback = Values::NONE,
        string $conferenceStatusCallbackMethod = Values::NONE,
        array $conferenceStatusCallbackEvent = Values::ARRAY_NONE,
        string $conferenceRecord = Values::NONE,
        string $conferenceTrim = Values::NONE,
        string $recordingChannels = Values::NONE,
        string $recordingStatusCallback = Values::NONE,
        string $recordingStatusCallbackMethod = Values::NONE,
        string $conferenceRecordingStatusCallback = Values::NONE,
        string $conferenceRecordingStatusCallbackMethod = Values::NONE,
        string $region = Values::NONE,
        string $sipAuthUsername = Values::NONE,
        string $sipAuthPassword = Values::NONE,
        array $dequeueStatusCallbackEvent = Values::ARRAY_NONE,
        string $postWorkActivitySid = Values::NONE,
        bool $endConferenceOnCustomerExit = Values::BOOL_NONE,
        bool $beepOnCustomerEntrance = Values::BOOL_NONE,
        string $jitterBufferSize = Values::NONE,
        string $ifMatch = Values::NONE

    ) {
        $this->options['reservationStatus'] = $reservationStatus;
        $this->options['workerActivitySid'] = $workerActivitySid;
        $this->options['instruction'] = $instruction;
        $this->options['dequeuePostWorkActivitySid'] = $dequeuePostWorkActivitySid;
        $this->options['dequeueFrom'] = $dequeueFrom;
        $this->options['dequeueRecord'] = $dequeueRecord;
        $this->options['dequeueTimeout'] = $dequeueTimeout;
        $this->options['dequeueTo'] = $dequeueTo;
        $this->options['dequeueStatusCallbackUrl'] = $dequeueStatusCallbackUrl;
        $this->options['callFrom'] = $callFrom;
        $this->options['callRecord'] = $callRecord;
        $this->options['callTimeout'] = $callTimeout;
        $this->options['callTo'] = $callTo;
        $this->options['callUrl'] = $callUrl;
        $this->options['callStatusCallbackUrl'] = $callStatusCallbackUrl;
        $this->options['callAccept'] = $callAccept;
        $this->options['redirectCallSid'] = $redirectCallSid;
        $this->options['redirectAccept'] = $redirectAccept;
        $this->options['redirectUrl'] = $redirectUrl;
        $this->options['to'] = $to;
        $this->options['from'] = $from;
        $this->options['statusCallback'] = $statusCallback;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        $this->options['timeout'] = $timeout;
        $this->options['record'] = $record;
        $this->options['muted'] = $muted;
        $this->options['beep'] = $beep;
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        $this->options['waitUrl'] = $waitUrl;
        $this->options['waitMethod'] = $waitMethod;
        $this->options['earlyMedia'] = $earlyMedia;
        $this->options['maxParticipants'] = $maxParticipants;
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        $this->options['conferenceRecord'] = $conferenceRecord;
        $this->options['conferenceTrim'] = $conferenceTrim;
        $this->options['recordingChannels'] = $recordingChannels;
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        $this->options['region'] = $region;
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        $this->options['dequeueStatusCallbackEvent'] = $dequeueStatusCallbackEvent;
        $this->options['postWorkActivitySid'] = $postWorkActivitySid;
        $this->options['endConferenceOnCustomerExit'] = $endConferenceOnCustomerExit;
        $this->options['beepOnCustomerEntrance'] = $beepOnCustomerEntrance;
        $this->options['jitterBufferSize'] = $jitterBufferSize;
        $this->options['ifMatch'] = $ifMatch;
    }

    /**
     * @param string $reservationStatus
     * @return $this Fluent Builder
     */
    public function setReservationStatus(string $reservationStatus): self
    {
        $this->options['reservationStatus'] = $reservationStatus;
        return $this;
    }

    /**
     * The new worker activity SID if rejecting a reservation.
     *
     * @param string $workerActivitySid The new worker activity SID if rejecting a reservation.
     * @return $this Fluent Builder
     */
    public function setWorkerActivitySid(string $workerActivitySid): self
    {
        $this->options['workerActivitySid'] = $workerActivitySid;
        return $this;
    }

    /**
     * The assignment instruction for the reservation.
     *
     * @param string $instruction The assignment instruction for the reservation.
     * @return $this Fluent Builder
     */
    public function setInstruction(string $instruction): self
    {
        $this->options['instruction'] = $instruction;
        return $this;
    }

    /**
     * The SID of the Activity resource to start after executing a Dequeue instruction.
     *
     * @param string $dequeuePostWorkActivitySid The SID of the Activity resource to start after executing a Dequeue instruction.
     * @return $this Fluent Builder
     */
    public function setDequeuePostWorkActivitySid(string $dequeuePostWorkActivitySid): self
    {
        $this->options['dequeuePostWorkActivitySid'] = $dequeuePostWorkActivitySid;
        return $this;
    }

    /**
     * The caller ID of the call to the worker when executing a Dequeue instruction.
     *
     * @param string $dequeueFrom The caller ID of the call to the worker when executing a Dequeue instruction.
     * @return $this Fluent Builder
     */
    public function setDequeueFrom(string $dequeueFrom): self
    {
        $this->options['dequeueFrom'] = $dequeueFrom;
        return $this;
    }

    /**
     * Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
     *
     * @param string $dequeueRecord Whether to record both legs of a call when executing a Dequeue instruction or which leg to record.
     * @return $this Fluent Builder
     */
    public function setDequeueRecord(string $dequeueRecord): self
    {
        $this->options['dequeueRecord'] = $dequeueRecord;
        return $this;
    }

    /**
     * The timeout for call when executing a Dequeue instruction.
     *
     * @param int $dequeueTimeout The timeout for call when executing a Dequeue instruction.
     * @return $this Fluent Builder
     */
    public function setDequeueTimeout(int $dequeueTimeout): self
    {
        $this->options['dequeueTimeout'] = $dequeueTimeout;
        return $this;
    }

    /**
     * The contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     *
     * @param string $dequeueTo The contact URI of the worker when executing a Dequeue instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @return $this Fluent Builder
     */
    public function setDequeueTo(string $dequeueTo): self
    {
        $this->options['dequeueTo'] = $dequeueTo;
        return $this;
    }

    /**
     * The callback URL for completed call event when executing a Dequeue instruction.
     *
     * @param string $dequeueStatusCallbackUrl The callback URL for completed call event when executing a Dequeue instruction.
     * @return $this Fluent Builder
     */
    public function setDequeueStatusCallbackUrl(string $dequeueStatusCallbackUrl): self
    {
        $this->options['dequeueStatusCallbackUrl'] = $dequeueStatusCallbackUrl;
        return $this;
    }

    /**
     * The Caller ID of the outbound call when executing a Call instruction.
     *
     * @param string $callFrom The Caller ID of the outbound call when executing a Call instruction.
     * @return $this Fluent Builder
     */
    public function setCallFrom(string $callFrom): self
    {
        $this->options['callFrom'] = $callFrom;
        return $this;
    }

    /**
     * Whether to record both legs of a call when executing a Call instruction.
     *
     * @param string $callRecord Whether to record both legs of a call when executing a Call instruction.
     * @return $this Fluent Builder
     */
    public function setCallRecord(string $callRecord): self
    {
        $this->options['callRecord'] = $callRecord;
        return $this;
    }

    /**
     * The timeout for a call when executing a Call instruction.
     *
     * @param int $callTimeout The timeout for a call when executing a Call instruction.
     * @return $this Fluent Builder
     */
    public function setCallTimeout(int $callTimeout): self
    {
        $this->options['callTimeout'] = $callTimeout;
        return $this;
    }

    /**
     * The contact URI of the worker when executing a Call instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     *
     * @param string $callTo The contact URI of the worker when executing a Call instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @return $this Fluent Builder
     */
    public function setCallTo(string $callTo): self
    {
        $this->options['callTo'] = $callTo;
        return $this;
    }

    /**
     * TwiML URI executed on answering the worker's leg as a result of the Call instruction.
     *
     * @param string $callUrl TwiML URI executed on answering the worker's leg as a result of the Call instruction.
     * @return $this Fluent Builder
     */
    public function setCallUrl(string $callUrl): self
    {
        $this->options['callUrl'] = $callUrl;
        return $this;
    }

    /**
     * The URL to call for the completed call event when executing a Call instruction.
     *
     * @param string $callStatusCallbackUrl The URL to call for the completed call event when executing a Call instruction.
     * @return $this Fluent Builder
     */
    public function setCallStatusCallbackUrl(string $callStatusCallbackUrl): self
    {
        $this->options['callStatusCallbackUrl'] = $callStatusCallbackUrl;
        return $this;
    }

    /**
     * Whether to accept a reservation when executing a Call instruction.
     *
     * @param bool $callAccept Whether to accept a reservation when executing a Call instruction.
     * @return $this Fluent Builder
     */
    public function setCallAccept(bool $callAccept): self
    {
        $this->options['callAccept'] = $callAccept;
        return $this;
    }

    /**
     * The Call SID of the call parked in the queue when executing a Redirect instruction.
     *
     * @param string $redirectCallSid The Call SID of the call parked in the queue when executing a Redirect instruction.
     * @return $this Fluent Builder
     */
    public function setRedirectCallSid(string $redirectCallSid): self
    {
        $this->options['redirectCallSid'] = $redirectCallSid;
        return $this;
    }

    /**
     * Whether the reservation should be accepted when executing a Redirect instruction.
     *
     * @param bool $redirectAccept Whether the reservation should be accepted when executing a Redirect instruction.
     * @return $this Fluent Builder
     */
    public function setRedirectAccept(bool $redirectAccept): self
    {
        $this->options['redirectAccept'] = $redirectAccept;
        return $this;
    }

    /**
     * TwiML URI to redirect the call to when executing the Redirect instruction.
     *
     * @param string $redirectUrl TwiML URI to redirect the call to when executing the Redirect instruction.
     * @return $this Fluent Builder
     */
    public function setRedirectUrl(string $redirectUrl): self
    {
        $this->options['redirectUrl'] = $redirectUrl;
        return $this;
    }

    /**
     * The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     *
     * @param string $to The Contact URI of the worker when executing a Conference instruction. Can be the URI of the Twilio Client, the SIP URI for Programmable SIP, or the [E.164](https://www.twilio.com/docs/glossary/what-e164) formatted phone number, depending on the destination.
     * @return $this Fluent Builder
     */
    public function setTo(string $to): self
    {
        $this->options['to'] = $to;
        return $this;
    }

    /**
     * The caller ID of the call to the worker when executing a Conference instruction.
     *
     * @param string $from The caller ID of the call to the worker when executing a Conference instruction.
     * @return $this Fluent Builder
     */
    public function setFrom(string $from): self
    {
        $this->options['from'] = $from;
        return $this;
    }

    /**
     * The URL we should call using the `status_callback_method` to send status information to your application.
     *
     * @param string $statusCallback The URL we should call using the `status_callback_method` to send status information to your application.
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self
    {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
     *
     * @param string $statusCallbackMethod The HTTP method we should use to call `status_callback`. Can be: `POST` or `GET` and the default is `POST`.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self
    {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
     *
     * @param string $statusCallbackEvent The call progress events that we will send to `status_callback`. Can be: `initiated`, `ringing`, `answered`, or `completed`.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackEvent(array $statusCallbackEvent): self
    {
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        return $this;
    }

    /**
     * The timeout for a call when executing a Conference instruction.
     *
     * @param int $timeout The timeout for a call when executing a Conference instruction.
     * @return $this Fluent Builder
     */
    public function setTimeout(int $timeout): self
    {
        $this->options['timeout'] = $timeout;
        return $this;
    }

    /**
     * Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     *
     * @param bool $record Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setRecord(bool $record): self
    {
        $this->options['record'] = $record;
        return $this;
    }

    /**
     * Whether the agent is muted in the conference. Defaults to `false`.
     *
     * @param bool $muted Whether the agent is muted in the conference. Defaults to `false`.
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self
    {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     *
     * @param string $beep Whether to play a notification beep when the participant joins or when to play a beep. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     * @return $this Fluent Builder
     */
    public function setBeep(string $beep): self
    {
        $this->options['beep'] = $beep;
        return $this;
    }

    /**
     * Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     *
     * @param bool $startConferenceOnEnter Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     * @return $this Fluent Builder
     */
    public function setStartConferenceOnEnter(bool $startConferenceOnEnter): self
    {
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        return $this;
    }

    /**
     * Whether to end the conference when the agent leaves.
     *
     * @param bool $endConferenceOnExit Whether to end the conference when the agent leaves.
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnExit(bool $endConferenceOnExit): self
    {
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        return $this;
    }

    /**
     * The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     *
     * @param string $waitUrl The URL we should call using the `wait_method` for the music to play while participants are waiting for the conference to start. The default value is the URL of our standard hold music. [Learn more about hold music](https://www.twilio.com/labs/twimlets/holdmusic).
     * @return $this Fluent Builder
     */
    public function setWaitUrl(string $waitUrl): self
    {
        $this->options['waitUrl'] = $waitUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     *
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @return $this Fluent Builder
     */
    public function setWaitMethod(string $waitMethod): self
    {
        $this->options['waitMethod'] = $waitMethod;
        return $this;
    }

    /**
     * Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
     *
     * @param bool $earlyMedia Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. The default is `true`.
     * @return $this Fluent Builder
     */
    public function setEarlyMedia(bool $earlyMedia): self
    {
        $this->options['earlyMedia'] = $earlyMedia;
        return $this;
    }

    /**
     * The maximum number of participants allowed in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     *
     * @param int $maxParticipants The maximum number of participants allowed in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     * @return $this Fluent Builder
     */
    public function setMaxParticipants(int $maxParticipants): self
    {
        $this->options['maxParticipants'] = $maxParticipants;
        return $this;
    }

    /**
     * The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     *
     * @param string $conferenceStatusCallback The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallback(string $conferenceStatusCallback): self
    {
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceStatusCallbackMethod The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackMethod(string $conferenceStatusCallbackMethod): self
    {
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        return $this;
    }

    /**
     * The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
     *
     * @param string $conferenceStatusCallbackEvent The conference status events that we will send to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `speaker`.
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackEvent(array $conferenceStatusCallbackEvent): self
    {
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        return $this;
    }

    /**
     * Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     *
     * @param string $conferenceRecord Whether to record the conference the participant is joining or when to record the conference. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     * @return $this Fluent Builder
     */
    public function setConferenceRecord(string $conferenceRecord): self
    {
        $this->options['conferenceRecord'] = $conferenceRecord;
        return $this;
    }

    /**
     * Whether to trim leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     *
     * @param string $conferenceTrim Whether to trim leading and trailing silence from your recorded conference audio files. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     * @return $this Fluent Builder
     */
    public function setConferenceTrim(string $conferenceTrim): self
    {
        $this->options['conferenceTrim'] = $conferenceTrim;
        return $this;
    }

    /**
     * The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     *
     * @param string $recordingChannels The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     * @return $this Fluent Builder
     */
    public function setRecordingChannels(string $recordingChannels): self
    {
        $this->options['recordingChannels'] = $recordingChannels;
        return $this;
    }

    /**
     * The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     *
     * @param string $recordingStatusCallback The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallback(string $recordingStatusCallback): self
    {
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $recordingStatusCallbackMethod The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallbackMethod(string $recordingStatusCallbackMethod): self
    {
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     *
     * @param string $conferenceRecordingStatusCallback The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallback(string $conferenceRecordingStatusCallback): self
    {
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallbackMethod(string $conferenceRecordingStatusCallbackMethod): self
    {
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     *
     * @param string $region The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     * @return $this Fluent Builder
     */
    public function setRegion(string $region): self
    {
        $this->options['region'] = $region;
        return $this;
    }

    /**
     * The SIP username used for authentication.
     *
     * @param string $sipAuthUsername The SIP username used for authentication.
     * @return $this Fluent Builder
     */
    public function setSipAuthUsername(string $sipAuthUsername): self
    {
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        return $this;
    }

    /**
     * The SIP password for authentication.
     *
     * @param string $sipAuthPassword The SIP password for authentication.
     * @return $this Fluent Builder
     */
    public function setSipAuthPassword(string $sipAuthPassword): self
    {
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        return $this;
    }

    /**
     * The call progress events sent via webhooks as a result of a Dequeue instruction.
     *
     * @param string[] $dequeueStatusCallbackEvent The call progress events sent via webhooks as a result of a Dequeue instruction.
     * @return $this Fluent Builder
     */
    public function setDequeueStatusCallbackEvent(array $dequeueStatusCallbackEvent): self
    {
        $this->options['dequeueStatusCallbackEvent'] = $dequeueStatusCallbackEvent;
        return $this;
    }

    /**
     * The new worker activity SID after executing a Conference instruction.
     *
     * @param string $postWorkActivitySid The new worker activity SID after executing a Conference instruction.
     * @return $this Fluent Builder
     */
    public function setPostWorkActivitySid(string $postWorkActivitySid): self
    {
        $this->options['postWorkActivitySid'] = $postWorkActivitySid;
        return $this;
    }

    /**
     * Whether to end the conference when the customer leaves.
     *
     * @param bool $endConferenceOnCustomerExit Whether to end the conference when the customer leaves.
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnCustomerExit(bool $endConferenceOnCustomerExit): self
    {
        $this->options['endConferenceOnCustomerExit'] = $endConferenceOnCustomerExit;
        return $this;
    }

    /**
     * Whether to play a notification beep when the customer joins.
     *
     * @param bool $beepOnCustomerEntrance Whether to play a notification beep when the customer joins.
     * @return $this Fluent Builder
     */
    public function setBeepOnCustomerEntrance(bool $beepOnCustomerEntrance): self
    {
        $this->options['beepOnCustomerEntrance'] = $beepOnCustomerEntrance;
        return $this;
    }

    /**
     * The jitter buffer size for conference. Can be: `small`, `medium`, `large`, `off`.
     *
     * @param string $jitterBufferSize The jitter buffer size for conference. Can be: `small`, `medium`, `large`, `off`.
     * @return $this Fluent Builder
     */
    public function setJitterBufferSize(string $jitterBufferSize): self
    {
        $this->options['jitterBufferSize'] = $jitterBufferSize;
        return $this;
    }

    /**
     * The If-Match HTTP request header
     *
     * @param string $ifMatch The If-Match HTTP request header
     * @return $this Fluent Builder
     */
    public function setIfMatch(string $ifMatch): self
    {
        $this->options['ifMatch'] = $ifMatch;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.UpdateReservationOptions ' . $options . ']';
    }
}

