<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Video\V1\Room;

use Twilio\Options;
use Twilio\Values;

abstract class TranscriptionsOptions
{
    /**
     * @param array $configuration A collection of properties that describe transcription behaviour.
     * @return CreateTranscriptionsOptions Options builder
     */
    public static function create(
        
        array $configuration = Values::ARRAY_NONE

    ): CreateTranscriptionsOptions
    {
        return new CreateTranscriptionsOptions(
            $configuration
        );
    }



    /**
     * @param string $status
     * @return UpdateTranscriptionsOptions Options builder
     */
    public static function update(
        
        string $status = Values::NONE

    ): UpdateTranscriptionsOptions
    {
        return new UpdateTranscriptionsOptions(
            $status
        );
    }

}

class CreateTranscriptionsOptions extends Options
    {
    /**
     * @param array $configuration A collection of properties that describe transcription behaviour.
     */
    public function __construct(
        
        array $configuration = Values::ARRAY_NONE

    ) {
        $this->options['configuration'] = $configuration;
    }

    /**
     * A collection of properties that describe transcription behaviour.
     *
     * @param array $configuration A collection of properties that describe transcription behaviour.
     * @return $this Fluent Builder
     */
    public function setConfiguration(array $configuration): self
    {
        $this->options['configuration'] = $configuration;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Video.V1.CreateTranscriptionsOptions ' . $options . ']';
    }
}



class UpdateTranscriptionsOptions extends Options
    {
    /**
     * @param string $status
     */
    public function __construct(
        
        string $status = Values::NONE

    ) {
        $this->options['status'] = $status;
    }

    /**
     * @param string $status
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Video.V1.UpdateTranscriptionsOptions ' . $options . ']';
    }
}

