<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Make email nullable and remove unique constraint
            $table->string('email')->nullable()->change();
            $table->dropUnique(['email']);

            // Make password nullable (for mobile-only accounts)
            $table->string('password')->nullable()->change();

            // Add mobile verification fields (only if they don't exist)
            if (!Schema::hasColumn('users', 'phone_verified_at')) {
                $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at');
            }
            if (!Schema::hasColumn('users', 'is_mobile_verified')) {
                $table->boolean('is_mobile_verified')->default(false)->after('phone_verified_at');
            }
        });

        // Update existing users to have unique phone numbers or generate them
        $users = \App\Models\User::whereNull('phone')->orWhere('phone', '')->get();
        foreach ($users as $user) {
            $user->update(['phone' => '9999999' . str_pad($user->id, 3, '0', STR_PAD_LEFT)]);
        }

        // Now add unique constraint to phone
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone', 15)->nullable(false)->change();
            $table->unique('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Revert email changes
            $table->string('email')->nullable(false)->change();
            $table->unique('email');

            // Revert phone changes
            $table->string('phone', 15)->nullable()->change();
            $table->dropUnique(['phone']);

            // Revert password changes
            $table->string('password')->nullable(false)->change();

            // Remove mobile verification fields
            $table->dropColumn(['phone_verified_at', 'is_mobile_verified', 'login_count']);
        });
    }
};
