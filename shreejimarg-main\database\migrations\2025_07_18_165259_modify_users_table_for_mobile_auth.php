<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Make email nullable and remove unique constraint
            $table->string('email')->nullable()->change();
            $table->dropUnique(['email']);

            // Make phone required and unique
            $table->string('phone', 15)->nullable(false)->change();
            $table->unique('phone');

            // Make password nullable (for mobile-only accounts)
            $table->string('password')->nullable()->change();

            // Add mobile verification fields
            $table->timestamp('phone_verified_at')->nullable()->after('email_verified_at');
            $table->boolean('is_mobile_verified')->default(false)->after('phone_verified_at');

            // Add login count for tracking
            $table->integer('login_count')->default(0)->after('is_mobile_verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Revert email changes
            $table->string('email')->nullable(false)->change();
            $table->unique('email');

            // Revert phone changes
            $table->string('phone', 15)->nullable()->change();
            $table->dropUnique(['phone']);

            // Revert password changes
            $table->string('password')->nullable(false)->change();

            // Remove mobile verification fields
            $table->dropColumn(['phone_verified_at', 'is_mobile_verified', 'login_count']);
        });
    }
};
