@extends('layouts.admin')

@section('title', 'Manage Categories - Admin - ShreeJi Jewelry')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Category Management</h1>
        <p class="mb-0 text-muted">Manage product categories and collections</p>
    </div>
    <div>
        <a href="{{ route('admin.categories.create') }}" class="btn btn-primary-pink">
            <i class="fas fa-plus me-2"></i>Add New Category
        </a>
    </div>
</div>

<!-- Categories Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Categories</h5>
            </div>
            <div class="col-auto">
                <small class="text-muted">{{ $categories->total() }} total categories</small>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        @if($categories->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Image</th>
                        <th>Name</th>
                        <th>Products</th>
                        <th>Sort Order</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Created</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($categories as $category)
                    <tr>
                        <td>
                            @if($category->image)
                                <img src="{{ asset('storage/' . $category->image) }}" 
                                     alt="{{ $category->name }}" 
                                     class="rounded" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            @endif
                        </td>
                        <td>
                            <div>
                                <h6 class="mb-1">{{ $category->name }}</h6>
                                @if($category->description)
                                    <small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $category->products_count }} products</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ $category->sort_order }}</span>
                        </td>
                        <td>
                            @if($category->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-danger">Inactive</span>
                            @endif
                        </td>
                        <td>
                            @if($category->is_featured)
                                <span class="badge" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            <small class="text-muted">{{ $category->created_at->format('M d, Y') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ route('admin.categories.edit', $category->id) }}" 
                                   class="btn btn-outline-primary" 
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-outline-danger delete-category" 
                                        data-id="{{ $category->id }}"
                                        data-name="{{ $category->name }}"
                                        data-products="{{ $category->products_count }}"
                                        title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($categories->hasPages())
        <div class="card-footer bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        Showing {{ $categories->firstItem() }} to {{ $categories->lastItem() }} 
                        of {{ $categories->total() }} categories
                    </small>
                </div>
                <div>
                    {{ $categories->links() }}
                </div>
            </div>
        </div>
        @endif
        @else
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Categories Found</h5>
            <p class="text-muted mb-4">Start by creating your first product category.</p>
            <a href="{{ route('admin.categories.create') }}" class="btn btn-primary-pink">
                <i class="fas fa-plus me-2"></i>Add First Category
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the category <strong id="categoryName"></strong>?</p>
                <div id="productsWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This category has <strong id="productsCount"></strong> products. 
                    Please move or delete the products first.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete Category</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    let categoryToDelete = null;

    // Handle delete button clicks
    document.querySelectorAll('.delete-category').forEach(button => {
        button.addEventListener('click', function() {
            categoryToDelete = this.dataset.id;
            const categoryName = this.dataset.name;
            const productsCount = parseInt(this.dataset.products);

            document.getElementById('categoryName').textContent = categoryName;
            document.getElementById('productsCount').textContent = productsCount;

            const productsWarning = document.getElementById('productsWarning');
            const confirmButton = document.getElementById('confirmDelete');

            if (productsCount > 0) {
                productsWarning.style.display = 'block';
                confirmButton.disabled = true;
                confirmButton.textContent = 'Cannot Delete';
            } else {
                productsWarning.style.display = 'none';
                confirmButton.disabled = false;
                confirmButton.textContent = 'Delete Category';
            }

            deleteModal.show();
        });
    });

    // Handle delete confirmation
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (!categoryToDelete || this.disabled) return;

        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';

        fetch(`/admin/categories/${categoryToDelete}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                deleteModal.hide();
                location.reload();
            } else {
                throw new Error(data.message || 'Failed to delete category');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error: ' + error.message);
        })
        .finally(() => {
            this.disabled = false;
            this.textContent = 'Delete Category';
        });
    });
});
</script>
@endpush
