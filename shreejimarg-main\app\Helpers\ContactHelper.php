<?php

namespace App\Helpers;

class ContactHelper
{
    /**
     * Get phone number.
     */
    public static function phone(): string
    {
        return config('contact.phone');
    }

    /**
     * Get formatted phone number for display.
     */
    public static function phoneFormatted(): string
    {
        $phone = self::phone();
        return '+91-' . substr($phone, 0, 5) . '-' . substr($phone, 5);
    }

    /**
     * Get phone number for tel: links.
     */
    public static function phoneLink(): string
    {
        return '+91' . self::phone();
    }

    /**
     * Get email address.
     */
    public static function email(): string
    {
        return config('contact.email');
    }

    /**
     * Get WhatsApp number.
     */
    public static function whatsapp(): string
    {
        return config('contact.whatsapp');
    }

    /**
     * Get WhatsApp link.
     */
    public static function whatsappLink(string $message = ''): string
    {
        $number = '+91' . self::whatsapp();
        $encodedMessage = urlencode($message);
        return "https://wa.me/{$number}" . ($message ? "?text={$encodedMessage}" : '');
    }

    /**
     * Get Instagram username.
     */
    public static function instagram(): string
    {
        return config('contact.social.instagram.username');
    }

    /**
     * Get Instagram URL.
     */
    public static function instagramUrl(): string
    {
        return config('contact.social.instagram.url');
    }

    /**
     * Get all social media links.
     */
    public static function socialMedia(): array
    {
        return config('contact.social');
    }

    /**
     * Get full address.
     */
    public static function address(): string
    {
        return config('contact.address.full');
    }

    /**
     * Get address components.
     */
    public static function addressComponents(): array
    {
        return config('contact.address');
    }

    /**
     * Get business hours.
     */
    public static function businessHours(): array
    {
        return config('contact.business_hours');
    }

    /**
     * Get business hours display string.
     */
    public static function businessHoursDisplay(): string
    {
        return config('contact.business_hours.display');
    }

    /**
     * Get support contact info.
     */
    public static function support(): array
    {
        return config('contact.support');
    }

    /**
     * Get department contact info.
     */
    public static function department(string $department): array
    {
        return config("contact.departments.{$department}", []);
    }

    /**
     * Get all departments.
     */
    public static function departments(): array
    {
        return config('contact.departments');
    }

    /**
     * Generate contact card HTML.
     */
    public static function contactCard(): string
    {
        return view('components.contact-card')->render();
    }

    /**
     * Generate social media icons HTML.
     */
    public static function socialIcons(): string
    {
        return view('components.social-icons')->render();
    }
}
