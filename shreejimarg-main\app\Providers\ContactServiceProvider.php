<?php

namespace App\Providers;

use App\Helpers\ContactHelper;
use App\Http\View\Composers\SidebarComposer;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class ContactServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share contact information with all views
        View::share('contact', [
            'phone' => ContactHelper::phone(),
            'phoneFormatted' => ContactHelper::phoneFormatted(),
            'phoneLink' => ContactHelper::phoneLink(),
            'email' => ContactHelper::email(),
            'whatsapp' => ContactHelper::whatsapp(),
            'whatsappLink' => ContactHelper::whatsappLink(),
            'instagram' => ContactHelper::instagram(),
            'instagramUrl' => ContactHelper::instagramUrl(),
            'address' => ContactHelper::address(),
            'businessHours' => ContactHelper::businessHoursDisplay(),
            'social' => ContactHelper::socialMedia(),
        ]);

        // Register view composers
        View::composer('*', SidebarComposer::class);
    }
}
