<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace;

use Twilio\Options;
use Twilio\Values;

abstract class WorkflowOptions
{
    /**
     * @param string $assignmentCallbackUrl The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     * @param string $fallbackAssignmentCallbackUrl The URL that we should call when a call to the `assignment_callback_url` fails.
     * @param int $taskReservationTimeout How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     * @return CreateWorkflowOptions Options builder
     */
    public static function create(
        
        string $assignmentCallbackUrl = Values::NONE,
        string $fallbackAssignmentCallbackUrl = Values::NONE,
        int $taskReservationTimeout = Values::INT_NONE

    ): CreateWorkflowOptions
    {
        return new CreateWorkflowOptions(
            $assignmentCallbackUrl,
            $fallbackAssignmentCallbackUrl,
            $taskReservationTimeout
        );
    }



    /**
     * @param string $friendlyName The `friendly_name` of the Workflow resources to read.
     * @return ReadWorkflowOptions Options builder
     */
    public static function read(
        
        string $friendlyName = Values::NONE

    ): ReadWorkflowOptions
    {
        return new ReadWorkflowOptions(
            $friendlyName
        );
    }

    /**
     * @param string $friendlyName A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
     * @param string $assignmentCallbackUrl The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     * @param string $fallbackAssignmentCallbackUrl The URL that we should call when a call to the `assignment_callback_url` fails.
     * @param string $configuration A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
     * @param int $taskReservationTimeout How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     * @param string $reEvaluateTasks Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.
     * @return UpdateWorkflowOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $assignmentCallbackUrl = Values::NONE,
        string $fallbackAssignmentCallbackUrl = Values::NONE,
        string $configuration = Values::NONE,
        int $taskReservationTimeout = Values::INT_NONE,
        string $reEvaluateTasks = Values::NONE

    ): UpdateWorkflowOptions
    {
        return new UpdateWorkflowOptions(
            $friendlyName,
            $assignmentCallbackUrl,
            $fallbackAssignmentCallbackUrl,
            $configuration,
            $taskReservationTimeout,
            $reEvaluateTasks
        );
    }

}

class CreateWorkflowOptions extends Options
    {
    /**
     * @param string $assignmentCallbackUrl The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     * @param string $fallbackAssignmentCallbackUrl The URL that we should call when a call to the `assignment_callback_url` fails.
     * @param int $taskReservationTimeout How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     */
    public function __construct(
        
        string $assignmentCallbackUrl = Values::NONE,
        string $fallbackAssignmentCallbackUrl = Values::NONE,
        int $taskReservationTimeout = Values::INT_NONE

    ) {
        $this->options['assignmentCallbackUrl'] = $assignmentCallbackUrl;
        $this->options['fallbackAssignmentCallbackUrl'] = $fallbackAssignmentCallbackUrl;
        $this->options['taskReservationTimeout'] = $taskReservationTimeout;
    }

    /**
     * The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     *
     * @param string $assignmentCallbackUrl The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     * @return $this Fluent Builder
     */
    public function setAssignmentCallbackUrl(string $assignmentCallbackUrl): self
    {
        $this->options['assignmentCallbackUrl'] = $assignmentCallbackUrl;
        return $this;
    }

    /**
     * The URL that we should call when a call to the `assignment_callback_url` fails.
     *
     * @param string $fallbackAssignmentCallbackUrl The URL that we should call when a call to the `assignment_callback_url` fails.
     * @return $this Fluent Builder
     */
    public function setFallbackAssignmentCallbackUrl(string $fallbackAssignmentCallbackUrl): self
    {
        $this->options['fallbackAssignmentCallbackUrl'] = $fallbackAssignmentCallbackUrl;
        return $this;
    }

    /**
     * How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     *
     * @param int $taskReservationTimeout How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     * @return $this Fluent Builder
     */
    public function setTaskReservationTimeout(int $taskReservationTimeout): self
    {
        $this->options['taskReservationTimeout'] = $taskReservationTimeout;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.CreateWorkflowOptions ' . $options . ']';
    }
}



class ReadWorkflowOptions extends Options
    {
    /**
     * @param string $friendlyName The `friendly_name` of the Workflow resources to read.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * The `friendly_name` of the Workflow resources to read.
     *
     * @param string $friendlyName The `friendly_name` of the Workflow resources to read.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.ReadWorkflowOptions ' . $options . ']';
    }
}

class UpdateWorkflowOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
     * @param string $assignmentCallbackUrl The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     * @param string $fallbackAssignmentCallbackUrl The URL that we should call when a call to the `assignment_callback_url` fails.
     * @param string $configuration A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
     * @param int $taskReservationTimeout How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     * @param string $reEvaluateTasks Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $assignmentCallbackUrl = Values::NONE,
        string $fallbackAssignmentCallbackUrl = Values::NONE,
        string $configuration = Values::NONE,
        int $taskReservationTimeout = Values::INT_NONE,
        string $reEvaluateTasks = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['assignmentCallbackUrl'] = $assignmentCallbackUrl;
        $this->options['fallbackAssignmentCallbackUrl'] = $fallbackAssignmentCallbackUrl;
        $this->options['configuration'] = $configuration;
        $this->options['taskReservationTimeout'] = $taskReservationTimeout;
        $this->options['reEvaluateTasks'] = $reEvaluateTasks;
    }

    /**
     * A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
     *
     * @param string $friendlyName A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     *
     * @param string $assignmentCallbackUrl The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
     * @return $this Fluent Builder
     */
    public function setAssignmentCallbackUrl(string $assignmentCallbackUrl): self
    {
        $this->options['assignmentCallbackUrl'] = $assignmentCallbackUrl;
        return $this;
    }

    /**
     * The URL that we should call when a call to the `assignment_callback_url` fails.
     *
     * @param string $fallbackAssignmentCallbackUrl The URL that we should call when a call to the `assignment_callback_url` fails.
     * @return $this Fluent Builder
     */
    public function setFallbackAssignmentCallbackUrl(string $fallbackAssignmentCallbackUrl): self
    {
        $this->options['fallbackAssignmentCallbackUrl'] = $fallbackAssignmentCallbackUrl;
        return $this;
    }

    /**
     * A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
     *
     * @param string $configuration A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
     * @return $this Fluent Builder
     */
    public function setConfiguration(string $configuration): self
    {
        $this->options['configuration'] = $configuration;
        return $this;
    }

    /**
     * How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     *
     * @param int $taskReservationTimeout How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
     * @return $this Fluent Builder
     */
    public function setTaskReservationTimeout(int $taskReservationTimeout): self
    {
        $this->options['taskReservationTimeout'] = $taskReservationTimeout;
        return $this;
    }

    /**
     * Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.
     *
     * @param string $reEvaluateTasks Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.
     * @return $this Fluent Builder
     */
    public function setReEvaluateTasks(string $reEvaluateTasks): self
    {
        $this->options['reEvaluateTasks'] = $reEvaluateTasks;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.UpdateWorkflowOptions ' . $options . ']';
    }
}

