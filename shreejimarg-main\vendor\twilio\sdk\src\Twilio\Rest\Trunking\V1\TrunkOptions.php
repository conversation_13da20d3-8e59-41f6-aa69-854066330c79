<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trunking
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Trunking\V1;

use Twilio\Options;
use Twilio\Values;

abstract class TrunkOptions
{
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $domainName The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     * @param string $disasterRecoveryUrl The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     * @param string $disasterRecoveryMethod The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     * @param string $transferMode
     * @param bool $secure Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     * @param bool $cnamLookupEnabled Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     * @param string $transferCallerId
     * @return CreateTrunkOptions Options builder
     */
    public static function create(
        
        string $friendlyName = Values::NONE,
        string $domainName = Values::NONE,
        string $disasterRecoveryUrl = Values::NONE,
        string $disasterRecoveryMethod = Values::NONE,
        string $transferMode = Values::NONE,
        bool $secure = Values::BOOL_NONE,
        bool $cnamLookupEnabled = Values::BOOL_NONE,
        string $transferCallerId = Values::NONE

    ): CreateTrunkOptions
    {
        return new CreateTrunkOptions(
            $friendlyName,
            $domainName,
            $disasterRecoveryUrl,
            $disasterRecoveryMethod,
            $transferMode,
            $secure,
            $cnamLookupEnabled,
            $transferCallerId
        );
    }




    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $domainName The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     * @param string $disasterRecoveryUrl The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     * @param string $disasterRecoveryMethod The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     * @param string $transferMode
     * @param bool $secure Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     * @param bool $cnamLookupEnabled Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     * @param string $transferCallerId
     * @return UpdateTrunkOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        string $domainName = Values::NONE,
        string $disasterRecoveryUrl = Values::NONE,
        string $disasterRecoveryMethod = Values::NONE,
        string $transferMode = Values::NONE,
        bool $secure = Values::BOOL_NONE,
        bool $cnamLookupEnabled = Values::BOOL_NONE,
        string $transferCallerId = Values::NONE

    ): UpdateTrunkOptions
    {
        return new UpdateTrunkOptions(
            $friendlyName,
            $domainName,
            $disasterRecoveryUrl,
            $disasterRecoveryMethod,
            $transferMode,
            $secure,
            $cnamLookupEnabled,
            $transferCallerId
        );
    }

}

class CreateTrunkOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $domainName The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     * @param string $disasterRecoveryUrl The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     * @param string $disasterRecoveryMethod The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     * @param string $transferMode
     * @param bool $secure Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     * @param bool $cnamLookupEnabled Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     * @param string $transferCallerId
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $domainName = Values::NONE,
        string $disasterRecoveryUrl = Values::NONE,
        string $disasterRecoveryMethod = Values::NONE,
        string $transferMode = Values::NONE,
        bool $secure = Values::BOOL_NONE,
        bool $cnamLookupEnabled = Values::BOOL_NONE,
        string $transferCallerId = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['domainName'] = $domainName;
        $this->options['disasterRecoveryUrl'] = $disasterRecoveryUrl;
        $this->options['disasterRecoveryMethod'] = $disasterRecoveryMethod;
        $this->options['transferMode'] = $transferMode;
        $this->options['secure'] = $secure;
        $this->options['cnamLookupEnabled'] = $cnamLookupEnabled;
        $this->options['transferCallerId'] = $transferCallerId;
    }

    /**
     * A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     *
     * @param string $domainName The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     * @return $this Fluent Builder
     */
    public function setDomainName(string $domainName): self
    {
        $this->options['domainName'] = $domainName;
        return $this;
    }

    /**
     * The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     *
     * @param string $disasterRecoveryUrl The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     * @return $this Fluent Builder
     */
    public function setDisasterRecoveryUrl(string $disasterRecoveryUrl): self
    {
        $this->options['disasterRecoveryUrl'] = $disasterRecoveryUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     *
     * @param string $disasterRecoveryMethod The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     * @return $this Fluent Builder
     */
    public function setDisasterRecoveryMethod(string $disasterRecoveryMethod): self
    {
        $this->options['disasterRecoveryMethod'] = $disasterRecoveryMethod;
        return $this;
    }

    /**
     * @param string $transferMode
     * @return $this Fluent Builder
     */
    public function setTransferMode(string $transferMode): self
    {
        $this->options['transferMode'] = $transferMode;
        return $this;
    }

    /**
     * Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     *
     * @param bool $secure Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     * @return $this Fluent Builder
     */
    public function setSecure(bool $secure): self
    {
        $this->options['secure'] = $secure;
        return $this;
    }

    /**
     * Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     *
     * @param bool $cnamLookupEnabled Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     * @return $this Fluent Builder
     */
    public function setCnamLookupEnabled(bool $cnamLookupEnabled): self
    {
        $this->options['cnamLookupEnabled'] = $cnamLookupEnabled;
        return $this;
    }

    /**
     * @param string $transferCallerId
     * @return $this Fluent Builder
     */
    public function setTransferCallerId(string $transferCallerId): self
    {
        $this->options['transferCallerId'] = $transferCallerId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Trunking.V1.CreateTrunkOptions ' . $options . ']';
    }
}




class UpdateTrunkOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @param string $domainName The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     * @param string $disasterRecoveryUrl The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     * @param string $disasterRecoveryMethod The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     * @param string $transferMode
     * @param bool $secure Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     * @param bool $cnamLookupEnabled Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     * @param string $transferCallerId
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        string $domainName = Values::NONE,
        string $disasterRecoveryUrl = Values::NONE,
        string $disasterRecoveryMethod = Values::NONE,
        string $transferMode = Values::NONE,
        bool $secure = Values::BOOL_NONE,
        bool $cnamLookupEnabled = Values::BOOL_NONE,
        string $transferCallerId = Values::NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['domainName'] = $domainName;
        $this->options['disasterRecoveryUrl'] = $disasterRecoveryUrl;
        $this->options['disasterRecoveryMethod'] = $disasterRecoveryMethod;
        $this->options['transferMode'] = $transferMode;
        $this->options['secure'] = $secure;
        $this->options['cnamLookupEnabled'] = $cnamLookupEnabled;
        $this->options['transferCallerId'] = $transferCallerId;
    }

    /**
     * A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     *
     * @param string $domainName The unique address you reserve on Twilio to which you route your SIP traffic. Domain names can contain letters, digits, and `-` and must end with `pstn.twilio.com`. See [Termination Settings](https://www.twilio.com/docs/sip-trunking#termination) for more information.
     * @return $this Fluent Builder
     */
    public function setDomainName(string $domainName): self
    {
        $this->options['domainName'] = $domainName;
        return $this;
    }

    /**
     * The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     *
     * @param string $disasterRecoveryUrl The URL we should call using the `disaster_recovery_method` if an error occurs while sending SIP traffic towards the configured Origination URL. We retrieve TwiML from the URL and execute the instructions like any other normal TwiML call. See [Disaster Recovery](https://www.twilio.com/docs/sip-trunking#disaster-recovery) for more information.
     * @return $this Fluent Builder
     */
    public function setDisasterRecoveryUrl(string $disasterRecoveryUrl): self
    {
        $this->options['disasterRecoveryUrl'] = $disasterRecoveryUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     *
     * @param string $disasterRecoveryMethod The HTTP method we should use to call the `disaster_recovery_url`. Can be: `GET` or `POST`.
     * @return $this Fluent Builder
     */
    public function setDisasterRecoveryMethod(string $disasterRecoveryMethod): self
    {
        $this->options['disasterRecoveryMethod'] = $disasterRecoveryMethod;
        return $this;
    }

    /**
     * @param string $transferMode
     * @return $this Fluent Builder
     */
    public function setTransferMode(string $transferMode): self
    {
        $this->options['transferMode'] = $transferMode;
        return $this;
    }

    /**
     * Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     *
     * @param bool $secure Whether Secure Trunking is enabled for the trunk. If enabled, all calls going through the trunk will be secure using SRTP for media and TLS for signaling. If disabled, then RTP will be used for media. See [Secure Trunking](https://www.twilio.com/docs/sip-trunking#securetrunking) for more information.
     * @return $this Fluent Builder
     */
    public function setSecure(bool $secure): self
    {
        $this->options['secure'] = $secure;
        return $this;
    }

    /**
     * Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     *
     * @param bool $cnamLookupEnabled Whether Caller ID Name (CNAM) lookup should be enabled for the trunk. If enabled, all inbound calls to the SIP Trunk from the United States and Canada automatically perform a CNAM Lookup and display Caller ID data on your phone. See [CNAM Lookups](https://www.twilio.com/docs/sip-trunking#CNAM) for more information.
     * @return $this Fluent Builder
     */
    public function setCnamLookupEnabled(bool $cnamLookupEnabled): self
    {
        $this->options['cnamLookupEnabled'] = $cnamLookupEnabled;
        return $this;
    }

    /**
     * @param string $transferCallerId
     * @return $this Fluent Builder
     */
    public function setTransferCallerId(string $transferCallerId): self
    {
        $this->options['transferCallerId'] = $transferCallerId;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Trunking.V1.UpdateTrunkOptions ' . $options . ']';
    }
}

