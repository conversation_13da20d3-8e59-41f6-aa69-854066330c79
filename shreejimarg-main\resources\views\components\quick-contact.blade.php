@props(['title' => 'Quick Contact', 'showTitle' => true])

<div class="quick-contact-widget" {{ $attributes }}>
    @if($showTitle)
    <h6 class="mb-3">{{ $title }}</h6>
    @endif
    
    <div class="d-grid gap-2">
        <!-- Call Button -->
        <a href="tel:{{ $contact['phoneLink'] }}" class="btn btn-primary-pink btn-sm">
            <i class="fas fa-phone me-2"></i>Call Now
            <small class="d-block">{{ $contact['phoneFormatted'] }}</small>
        </a>
        
        <!-- WhatsApp Button -->
        <a href="{{ $contact['whatsappLink'] }}" target="_blank" class="btn btn-success btn-sm">
            <i class="fab fa-whatsapp me-2"></i>WhatsApp
            <small class="d-block">Chat with us</small>
        </a>
        
        <!-- Email <PERSON> -->
        <a href="mailto:{{ $contact['email'] }}" class="btn btn-outline-primary-pink btn-sm">
            <i class="fas fa-envelope me-2"></i>Email Us
            <small class="d-block">{{ $contact['email'] }}</small>
        </a>
    </div>
    
    <!-- Business Hours -->
    <div class="mt-3 p-2 bg-light rounded text-center">
        <small class="text-muted">
            <i class="fas fa-clock me-1"></i>
            {{ $contact['businessHours'] }}
        </small>
    </div>
    
    <!-- Social Links -->
    <div class="text-center mt-3">
        <small class="text-muted d-block mb-2">Follow Us</small>
        <div class="d-flex justify-content-center gap-1">
            @foreach($contact['social'] as $platform => $details)
            <a href="{{ $details['url'] }}" target="_blank" 
               class="btn btn-sm btn-outline-secondary rounded-circle" 
               title="{{ ucfirst($platform) }}">
                <i class="fab fa-{{ $platform === 'facebook' ? 'facebook-f' : $platform }}"></i>
            </a>
            @endforeach
        </div>
    </div>
</div>
