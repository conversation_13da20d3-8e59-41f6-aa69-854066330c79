<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\Category;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class UserOrdersPageTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $orders;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
            'total_spent' => 150000,
            'reward_points' => 1500,
        ]);

        // Create test category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create test products
        $product1 = Product::create([
            'category_id' => $category->id,
            'name' => 'Diamond Ring',
            'slug' => 'diamond-ring',
            'sku' => 'RING-001',
            'description' => 'Beautiful diamond ring',
            'price' => 50000.00,
            'stock_quantity' => 10,
            'in_stock' => true,
            'status' => 'active',
            'images' => ['https://example.com/ring.jpg'],
            'specifications' => ['metal' => 'gold'],
            'sizes' => ['6', '7', '8'],
        ]);

        $product2 = Product::create([
            'category_id' => $category->id,
            'name' => 'Pearl Necklace',
            'slug' => 'pearl-necklace',
            'sku' => 'NECK-001',
            'description' => 'Elegant pearl necklace',
            'price' => 25000.00,
            'stock_quantity' => 5,
            'in_stock' => true,
            'status' => 'active',
            'images' => ['https://example.com/necklace.jpg'],
            'specifications' => ['length' => '18 inches'],
            'sizes' => ['One Size'],
        ]);

        // Create test orders with different statuses
        $this->orders = collect([
            Order::create([
                'user_id' => $this->user->id,
                'order_number' => 'ORD-2024-001',
                'subtotal' => 50000.00,
                'tax_amount' => 9000.00,
                'shipping_amount' => 0.00,
                'discount_amount' => 0.00,
                'total_amount' => 59000.00,
                'currency' => 'INR',
                'status' => 'delivered',
                'payment_status' => 'paid',
                'payment_method' => 'razorpay',
                'delivered_at' => now()->subDays(5),
                'shipping_address' => [
                    'address' => 'Test Address',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'pincode' => '123456',
                    'country' => 'india'
                ],
                'billing_address' => [
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'phone' => '1234567890'
                ]
            ]),
            Order::create([
                'user_id' => $this->user->id,
                'order_number' => 'ORD-2024-002',
                'subtotal' => 25000.00,
                'tax_amount' => 4500.00,
                'shipping_amount' => 0.00,
                'discount_amount' => 0.00,
                'total_amount' => 29500.00,
                'currency' => 'INR',
                'status' => 'shipped',
                'payment_status' => 'paid',
                'payment_method' => 'razorpay',
                'shipped_at' => now()->subDays(2),
                'shipping_address' => [
                    'address' => 'Test Address',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'pincode' => '123456',
                    'country' => 'india'
                ],
                'billing_address' => [
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'phone' => '1234567890'
                ]
            ]),
            Order::create([
                'user_id' => $this->user->id,
                'order_number' => 'ORD-2024-003',
                'subtotal' => 75000.00,
                'tax_amount' => 13500.00,
                'shipping_amount' => 0.00,
                'discount_amount' => 0.00,
                'total_amount' => 88500.00,
                'currency' => 'INR',
                'status' => 'processing',
                'payment_status' => 'paid',
                'payment_method' => 'razorpay',
                'shipping_address' => [
                    'address' => 'Test Address',
                    'city' => 'Test City',
                    'state' => 'Test State',
                    'pincode' => '123456',
                    'country' => 'india'
                ],
                'billing_address' => [
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'phone' => '1234567890'
                ]
            ])
        ]);

        // Create order items
        OrderItem::create([
            'order_id' => $this->orders[0]->id,
            'product_id' => $product1->id,
            'product_name' => $product1->name,
            'product_sku' => $product1->sku,
            'quantity' => 1,
            'price' => 50000.00,
            'total' => 50000.00,
            'size' => '7'
        ]);

        OrderItem::create([
            'order_id' => $this->orders[1]->id,
            'product_id' => $product2->id,
            'product_name' => $product2->name,
            'product_sku' => $product2->sku,
            'quantity' => 1,
            'price' => 25000.00,
            'total' => 25000.00
        ]);

        OrderItem::create([
            'order_id' => $this->orders[2]->id,
            'product_id' => $product1->id,
            'product_name' => $product1->name,
            'product_sku' => $product1->sku,
            'quantity' => 1,
            'price' => 75000.00,
            'total' => 75000.00,
            'size' => '8'
        ]);
    }

    /** @test */
    public function it_displays_orders_page_with_dynamic_data()
    {
        $response = $this->actingAs($this->user)
            ->get('/orders');

        $response->assertStatus(200)
            ->assertViewIs('user.orders')
            ->assertViewHas('orders')
            ->assertSee('ORD-2024-001')
            ->assertSee('ORD-2024-002')
            ->assertSee('ORD-2024-003')
            ->assertSee('Diamond Ring')
            ->assertSee('Pearl Necklace');
    }

    /** @test */
    public function it_displays_correct_order_statuses()
    {
        $response = $this->actingAs($this->user)
            ->get('/orders');

        $response->assertStatus(200)
            ->assertSee('Delivered')
            ->assertSee('Shipped')
            ->assertSee('Processing');
    }

    /** @test */
    public function it_displays_user_statistics_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get('/orders');

        $response->assertStatus(200)
            ->assertSee('3') // Total orders
            ->assertSee('₹1,50,000') // Total spent
            ->assertSee('1500') // Reward points
            ->assertSee('Gold'); // Member status (based on total spent)
    }

    /** @test */
    public function it_shows_appropriate_action_buttons_based_on_status()
    {
        $response = $this->actingAs($this->user)
            ->get('/orders');

        $response->assertStatus(200)
            ->assertSee('View Details')
            ->assertSee('Download Invoice') // For delivered order
            ->assertSee('Track Package') // For shipped order
            ->assertSee('Rate &amp;amp;amp; Review'); // For delivered order (triple HTML encoded)
    }

    /** @test */
    public function it_shows_empty_state_when_no_orders()
    {
        // Create a new user with no orders
        $newUser = User::create([
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        $response = $this->actingAs($newUser)
            ->get('/orders');

        $response->assertStatus(200)
            ->assertSee('No Orders Found')
            ->assertSee('Start Shopping');
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->get('/orders');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function it_can_cancel_cancellable_orders()
    {
        // Create a cancellable order
        $cancellableOrder = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'ORD-2024-004',
            'subtotal' => 10000.00,
            'tax_amount' => 1800.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 11800.00,
            'currency' => 'INR',
            'status' => 'confirmed',
            'payment_status' => 'pending',
            'payment_method' => 'cod',
            'shipping_address' => ['address' => 'Test'],
            'billing_address' => ['name' => 'Test']
        ]);

        $response = $this->actingAs($this->user)
            ->putJson("/orders/{$cancellableOrder->id}/cancel");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order cancelled successfully!'
            ]);

        $cancellableOrder->refresh();
        $this->assertEquals('cancelled', $cancellableOrder->status);
    }

    /** @test */
    public function it_prevents_cancelling_non_cancellable_orders()
    {
        $response = $this->actingAs($this->user)
            ->putJson("/orders/{$this->orders[0]->id}/cancel"); // Delivered order

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'This order cannot be cancelled.'
            ]);
    }

    /** @test */
    public function it_prevents_unauthorized_order_cancellation()
    {
        $otherUser = User::create([
            'name' => 'Other User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        $response = $this->actingAs($otherUser)
            ->putJson("/orders/{$this->orders[0]->id}/cancel");

        $response->assertStatus(404);
    }
}
