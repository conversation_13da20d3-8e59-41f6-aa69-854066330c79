<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TwilioService;

class TestTwilioConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twilio:test {--phone= : Phone number to send test SMS}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Twilio connection and send a test SMS';

    /**
     * Execute the console command.
     */
    public function handle(TwilioService $twilioService)
    {
        $this->info('Testing Twilio Connection...');

        // Test connection
        $connectionTest = $twilioService->testConnection();

        if ($connectionTest['success']) {
            $this->info('✅ Twilio connection successful!');
            $this->info('Account SID: ' . $connectionTest['account_sid']);
            $this->info('Account Status: ' . $connectionTest['account_status']);
        } else {
            $this->error('❌ Twilio connection failed!');
            $this->error('Error: ' . $connectionTest['error']);
            return 1;
        }

        // Test SMS sending if phone number provided
        $phone = $this->option('phone');
        if ($phone) {
            $this->info("\nSending test SMS to: " . $phone);

            $smsResult = $twilioService->sendOtp($phone, '123456', 'test');

            if ($smsResult['success']) {
                $this->info('✅ Test SMS sent successfully!');
                $this->info('Message SID: ' . $smsResult['message_sid']);
            } else {
                $this->error('❌ Failed to send test SMS!');
                $this->error('Error: ' . $smsResult['error']);
                return 1;
            }
        } else {
            $this->info("\nTo test SMS sending, run:");
            $this->info('php artisan twilio:test --phone=**********');
        }

        $this->info("\n🎉 Twilio service is ready for mobile authentication!");
        return 0;
    }
}
