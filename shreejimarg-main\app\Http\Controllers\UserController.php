<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function dashboard()
    {
        try {
            $user = Auth::user();

            // Get recent orders
            $recentOrders = $user->orders()
                ->with('orderItems.product')
                ->recent()
                ->limit(5)
                ->get();

            // Get wishlist items
            $wishlistItems = $user->wishlists()
                ->with('product')
                ->limit(4)
                ->get();

            // Calculate stats with defaults
            $totalOrders = $user->orders()->count();
            $totalSpent = $user->total_spent ?? 0;
            $rewardPoints = $user->reward_points ?? 0;
            $membershipLevel = $user->membership_level ?? 'regular';

            return view('user.dashboard', compact(
                'user', 'recentOrders', 'wishlistItems',
                'totalOrders', 'totalSpent', 'rewardPoints', 'membershipLevel'
            ));
        } catch (\Exception $e) {
            Log::error('Dashboard error: ' . $e->getMessage());

            // Return a simple dashboard with default values
            $user = Auth::user();
            $recentOrders = collect();
            $wishlistItems = collect();
            $totalOrders = 0;
            $totalSpent = 0;
            $rewardPoints = 0;
            $membershipLevel = 'regular';

            return view('user.dashboard', compact(
                'user', 'recentOrders', 'wishlistItems',
                'totalOrders', 'totalSpent', 'rewardPoints', 'membershipLevel'
            ));
        }
    }

    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other,prefer-not-to-say',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->only(['name', 'email', 'phone', 'date_of_birth', 'gender']);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        $user->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully!'
        ]);
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect.'
            ], 400);
        }

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password updated successfully!'
        ]);
    }

    public function updatePreferences(Request $request)
    {
        $request->validate([
            'language' => 'required|string|in:en,hi,mr,gu',
            'currency' => 'required|string|in:inr,usd,eur',
            'email_newsletter' => 'boolean',
            'sms_notifications' => 'boolean',
            'birthday_offers' => 'boolean',
            'new_arrivals' => 'boolean'
        ]);

        $preferences = [
            'language' => $request->language,
            'currency' => $request->currency,
            'notifications' => [
                'email_newsletter' => $request->boolean('email_newsletter'),
                'sms_notifications' => $request->boolean('sms_notifications'),
                'birthday_offers' => $request->boolean('birthday_offers'),
                'new_arrivals' => $request->boolean('new_arrivals')
            ]
        ];

        Auth::user()->update(['preferences' => $preferences]);

        return response()->json([
            'success' => true,
            'message' => 'Preferences updated successfully!'
        ]);
    }

    public function deleteAccount(Request $request)
    {
        $request->validate([
            'password' => 'required'
        ]);

        $user = Auth::user();

        if (!Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Password is incorrect.'
            ], 400);
        }

        // Check for pending orders
        $pendingOrders = $user->orders()->whereIn('status', ['pending', 'confirmed', 'processing'])->count();

        if ($pendingOrders > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete account with pending orders. Please contact support.'
            ], 400);
        }

        // Delete avatar
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        // Soft delete or anonymize user data
        $user->update([
            'name' => 'Deleted User',
            'email' => 'deleted_' . time() . '@example.com',
            'phone' => null,
            'avatar' => null,
            'status' => 'inactive'
        ]);

        Auth::logout();

        return response()->json([
            'success' => true,
            'message' => 'Account deleted successfully.',
            'redirect_url' => route('home')
        ]);
    }
}
