<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OtpVerification extends Model
{
    use HasFactory;

    protected $fillable = [
        'phone',
        'otp_code',
        'purpose',
        'expires_at',
        'is_verified',
        'verified_at',
        'attempts',
        'resend_count',
        'last_resent_at',
        'session_id',
        'ip_address',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'verified_at' => 'datetime',
        'last_resent_at' => 'datetime',
        'is_verified' => 'boolean',
    ];

    /**
     * Generate a new OTP for the given phone number
     */
    public static function generateOtp($phone, $purpose = 'login', $sessionId = null, $ipAddress = null)
    {
        // Clean up expired OTPs for this phone
        static::where('phone', $phone)
            ->where('expires_at', '<', now())
            ->delete();

        // Check if there's a recent unverified OTP (within 1 minute)
        $recentOtp = static::where('phone', $phone)
            ->where('purpose', $purpose)
            ->where('is_verified', false)
            ->where('created_at', '>', now()->subMinute())
            ->first();

        if ($recentOtp) {
            return [
                'success' => false,
                'message' => 'Please wait before requesting a new OTP.',
                'wait_time' => 60 - now()->diffInSeconds($recentOtp->created_at)
            ];
        }

        // Check resend limit (max 3 per 15 minutes)
        $recentResends = static::where('phone', $phone)
            ->where('created_at', '>', now()->subMinutes(15))
            ->count();

        if ($recentResends >= 3) {
            return [
                'success' => false,
                'message' => 'Too many OTP requests. Please try again after 15 minutes.',
                'wait_time' => 900 // 15 minutes
            ];
        }

        // Generate 6-digit OTP
        $otpCode = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

        // Create new OTP record
        $otp = static::create([
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => $purpose,
            'expires_at' => now()->addMinutes(5), // 5 minutes expiry
            'session_id' => $sessionId,
            'ip_address' => $ipAddress,
            'resend_count' => $recentResends,
        ]);

        return [
            'success' => true,
            'otp_id' => $otp->id,
            'otp_code' => $otpCode,
            'expires_at' => $otp->expires_at,
        ];
    }

    /**
     * Verify OTP code
     */
    public static function verifyOtp($phone, $otpCode, $purpose = 'login')
    {
        $otp = static::where('phone', $phone)
            ->where('purpose', $purpose)
            ->where('is_verified', false)
            ->where('expires_at', '>', now())
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$otp) {
            return [
                'success' => false,
                'message' => 'Invalid or expired OTP.'
            ];
        }

        // Increment attempts
        $otp->increment('attempts');

        // Check if too many attempts (max 3)
        if ($otp->attempts > 3) {
            $otp->update(['expires_at' => now()]); // Expire the OTP
            return [
                'success' => false,
                'message' => 'Too many failed attempts. Please request a new OTP.'
            ];
        }

        // Verify OTP code
        if ($otp->otp_code !== $otpCode) {
            return [
                'success' => false,
                'message' => 'Invalid OTP code.',
                'attempts_left' => 3 - $otp->attempts
            ];
        }

        // Mark as verified
        $otp->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        return [
            'success' => true,
            'message' => 'OTP verified successfully.',
            'otp_id' => $otp->id
        ];
    }

    /**
     * Check if phone number has been verified recently
     */
    public static function isPhoneVerified($phone, $purpose = 'login', $withinMinutes = 30)
    {
        return static::where('phone', $phone)
            ->where('purpose', $purpose)
            ->where('is_verified', true)
            ->where('verified_at', '>', now()->subMinutes($withinMinutes))
            ->exists();
    }

    /**
     * Clean up expired and old OTP records
     */
    public static function cleanup()
    {
        // Delete expired OTPs
        static::where('expires_at', '<', now())->delete();

        // Delete old verified OTPs (older than 24 hours)
        static::where('is_verified', true)
            ->where('verified_at', '<', now()->subDay())
            ->delete();
    }

    /**
     * Scope for active (non-expired) OTPs
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Scope for unverified OTPs
     */
    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }

    /**
     * Check if OTP is expired
     */
    public function isExpired()
    {
        return $this->expires_at < now();
    }

    /**
     * Get remaining time in seconds
     */
    public function getRemainingTimeAttribute()
    {
        if ($this->isExpired()) {
            return 0;
        }

        return now()->diffInSeconds($this->expires_at);
    }
}
