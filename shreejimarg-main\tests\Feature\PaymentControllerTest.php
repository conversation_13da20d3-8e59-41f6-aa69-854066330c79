<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\OrderItem;
use App\Services\RazorpayService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Mockery;

class PaymentControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $order;
    protected $razorpayService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create test product
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 100.00,
            'stock_quantity' => 10,
            'in_stock' => true,
            'status' => 'active',
        ]);

        // Create test order
        $this->order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST_' . time(),
            'subtotal' => 100.00,
            'tax_amount' => 0.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'razorpay',
            'shipping_address' => [
                'name' => 'Test User',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456'
            ],
            'billing_address' => [
                'name' => 'Test User',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456'
            ]
        ]);

        // Create order item
        OrderItem::create([
            'order_id' => $this->order->id,
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_sku' => $product->sku,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00
        ]);

        // Mock RazorpayService
        $this->razorpayService = Mockery::mock(RazorpayService::class);
        $this->app->instance(RazorpayService::class, $this->razorpayService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_create_payment_order_successfully()
    {
        // Mock successful Razorpay order creation
        $this->razorpayService
            ->shouldReceive('createOrder')
            ->once()
            ->with(Mockery::type(Order::class))
            ->andReturn([
                'success' => true,
                'order_id' => 'order_test_123',
                'amount' => 10000, // Amount in paise
                'currency' => 'INR',
                'key_id' => 'rzp_test_key'
            ]);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        if ($response->status() !== 200) {
            dump('Response:', $response->json());
            dump('Status:', $response->status());
            // Check if mock was called
            try {
                $this->razorpayService->shouldHaveReceived('createOrder');
            } catch (\Exception $e) {
                dump('Mock error:', $e->getMessage());
            }
        }

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'order_id' => 'order_test_123',
                'amount' => 10000,
                'currency' => 'INR',
                'key_id' => 'rzp_test_key'
            ]);
    }

    /** @test */
    public function it_fails_to_create_payment_order_for_already_paid_order()
    {
        // Mark order as paid
        $this->order->update(['payment_status' => 'paid']);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Order is already paid'
            ]);
    }

    /** @test */
    public function it_fails_to_create_payment_order_for_nonexistent_order()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => 99999
            ]);

        $response->assertStatus(404);
    }

    /** @test */
    public function it_handles_razorpay_service_failure_during_order_creation()
    {
        // Mock Razorpay service failure
        $this->razorpayService
            ->shouldReceive('createOrder')
            ->once()
            ->with($this->order)
            ->andReturn([
                'success' => false,
                'message' => 'Razorpay API error'
            ]);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Razorpay API error'
            ]);
    }

    /** @test */
    public function it_can_verify_payment_successfully()
    {
        Mail::fake();

        // Set up order with Razorpay order ID
        $this->order->update([
            'payment_gateway_order_id' => 'order_test_123'
        ]);

        // Mock successful payment verification
        $this->razorpayService
            ->shouldReceive('verifyPayment')
            ->once()
            ->with('order_test_123', 'pay_test_123', 'signature_test_123')
            ->andReturn([
                'success' => true,
                'payment_id' => 'pay_test_123'
            ]);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/verify', [
                'razorpay_order_id' => 'order_test_123',
                'razorpay_payment_id' => 'pay_test_123',
                'razorpay_signature' => 'signature_test_123',
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Payment verified successfully'
            ]);

        // Verify order status updated
        $this->order->refresh();
        $this->assertEquals('confirmed', $this->order->status);
        $this->assertEquals('paid', $this->order->payment_status);
        $this->assertEquals('pay_test_123', $this->order->payment_gateway_payment_id);

        // Verify email was queued
        Mail::assertQueued(\App\Mail\OrderConfirmation::class);
    }

    /** @test */
    public function it_fails_payment_verification_with_invalid_signature()
    {
        // Set up order with Razorpay order ID
        $this->order->update([
            'payment_gateway_order_id' => 'order_test_123'
        ]);

        // Mock failed payment verification
        $this->razorpayService
            ->shouldReceive('verifyPayment')
            ->once()
            ->with('order_test_123', 'pay_test_123', 'invalid_signature')
            ->andReturn([
                'success' => false,
                'error' => 'Invalid signature'
            ]);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/verify', [
                'razorpay_order_id' => 'order_test_123',
                'razorpay_payment_id' => 'pay_test_123',
                'razorpay_signature' => 'invalid_signature',
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Payment verification failed'
            ]);

        // Verify order status not updated
        $this->order->refresh();
        $this->assertEquals('pending', $this->order->status);
        $this->assertEquals('pending', $this->order->payment_status);
    }

    /** @test */
    public function it_requires_authentication_for_payment_operations()
    {
        $response = $this->postJson('/payment/create-order', [
            'order_id' => $this->order->id
        ]);

        $response->assertStatus(401);

        $response = $this->postJson('/payment/verify', [
            'razorpay_order_id' => 'order_test_123',
            'razorpay_payment_id' => 'pay_test_123',
            'razorpay_signature' => 'signature_test_123',
            'order_id' => $this->order->id
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_validates_required_fields_for_payment_verification()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/payment/verify', [
                'order_id' => $this->order->id
                // Missing required fields
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'razorpay_order_id',
                'razorpay_payment_id',
                'razorpay_signature'
            ]);
    }

    /** @test */
    public function it_can_get_test_payment_details_in_local_environment()
    {
        config(['app.env' => 'local']);

        $this->razorpayService
            ->shouldReceive('getTestCardDetails')
            ->once()
            ->andReturn([
                'success_cards' => [
                    [
                        'number' => '****************',
                        'cvv' => '123',
                        'expiry' => '12/25',
                        'name' => 'Test Card'
                    ]
                ]
            ]);

        $response = $this->get('/payment/test-details');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success_cards' => [
                    '*' => ['number', 'cvv', 'expiry', 'name']
                ]
            ]);
    }

    /** @test */
    public function it_denies_test_payment_details_in_production()
    {
        config(['app.env' => 'production']);

        $response = $this->get('/payment/test-details');

        $response->assertStatus(403)
            ->assertJson([
                'error' => 'Not available in production'
            ]);
    }
}
