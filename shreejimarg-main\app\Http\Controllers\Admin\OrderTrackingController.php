<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderTrackingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!Auth::user()->isAdmin()) {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    public function index(Request $request)
    {
        $query = Order::with(['user', 'orderItems.product'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by tracking number
        if ($request->filled('tracking_number')) {
            $query->where('tracking_number', 'like', '%' . $request->tracking_number . '%');
        }

        // Filter by order number
        if ($request->filled('order_number')) {
            $query->where('order_number', 'like', '%' . $request->order_number . '%');
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->paginate(20);

        // Get status counts for dashboard
        $statusCounts = [
            'pending' => Order::where('status', 'pending')->count(),
            'confirmed' => Order::where('status', 'confirmed')->count(),
            'processing' => Order::where('status', 'processing')->count(),
            'packed' => Order::where('status', 'packed')->count(),
            'shipped' => Order::where('status', 'shipped')->count(),
            'out_for_delivery' => Order::where('status', 'out_for_delivery')->count(),
            'delivered' => Order::where('status', 'delivered')->count(),
        ];

        return view('admin.orders.tracking', compact('orders', 'statusCounts'));
    }

    public function show($id)
    {
        $order = Order::with(['user', 'orderItems.product'])->findOrFail($id);
        
        return view('admin.orders.tracking-detail', compact('order'));
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,processing,packed,shipped,out_for_delivery,delivered,cancelled',
            'notes' => 'nullable|string|max:1000',
            'delivery_confirmation_method' => 'nullable|string|in:sms,email,call',
            'delivered_to' => 'nullable|string|max:255',
            'delivery_notes' => 'nullable|string|max:1000'
        ]);

        $order = Order::findOrFail($id);
        $adminUser = Auth::user();

        // Check if status transition is valid
        $allowedStatuses = $order->getNextPossibleStatuses();
        if (!empty($allowedStatuses) && !in_array($request->status, $allowedStatuses)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid status transition. Allowed statuses: ' . implode(', ', $allowedStatuses)
            ], 400);
        }

        // Update status
        $order->updateStatus($request->status, $adminUser, $request->notes);

        // Update delivery information if provided
        if ($request->status === 'delivered') {
            $order->update([
                'delivery_confirmation_method' => $request->delivery_confirmation_method,
                'delivered_to' => $request->delivered_to,
                'delivery_notes' => $request->delivery_notes
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully!',
            'order' => $order->fresh()
        ]);
    }

    public function addTracking(Request $request, $id)
    {
        $request->validate([
            'tracking_number' => 'required|string|max:255',
            'courier_service' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000'
        ]);

        $order = Order::findOrFail($id);
        $adminUser = Auth::user();

        $order->addTrackingNumber(
            $request->tracking_number,
            $request->courier_service,
            $adminUser
        );

        if ($request->notes) {
            $order->update(['admin_notes' => $request->notes]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Tracking information added successfully!',
            'order' => $order->fresh()
        ]);
    }

    public function bulkUpdateStatus(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id',
            'status' => 'required|string|in:confirmed,processing,packed,shipped,out_for_delivery,delivered,cancelled',
            'notes' => 'nullable|string|max:1000'
        ]);

        $adminUser = Auth::user();
        $updatedCount = 0;

        foreach ($request->order_ids as $orderId) {
            $order = Order::find($orderId);
            if ($order) {
                $allowedStatuses = $order->getNextPossibleStatuses();
                if (empty($allowedStatuses) || in_array($request->status, $allowedStatuses)) {
                    $order->updateStatus($request->status, $adminUser, $request->notes);
                    $updatedCount++;
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully updated {$updatedCount} orders.",
            'updated_count' => $updatedCount
        ]);
    }

    public function exportOrders(Request $request)
    {
        $query = Order::with(['user', 'orderItems.product']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->get();

        $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Order Number', 'Customer Name', 'Customer Email', 'Status', 
                'Total Amount', 'Tracking Number', 'Courier Service', 
                'Order Date', 'Shipped Date', 'Delivered Date'
            ]);

            // CSV data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->order_number,
                    $order->user->name,
                    $order->user->email,
                    $order->getStatusDisplayName(),
                    '₹' . number_format($order->total_amount, 2),
                    $order->tracking_number ?? 'N/A',
                    $order->courier_service ?? 'N/A',
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->shipped_at ? $order->shipped_at->format('Y-m-d H:i:s') : 'N/A',
                    $order->delivered_at ? $order->delivered_at->format('Y-m-d H:i:s') : 'N/A'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function getTrackingInfo($id)
    {
        $order = Order::findOrFail($id);
        
        return response()->json([
            'order_number' => $order->order_number,
            'status' => $order->status,
            'status_display' => $order->getStatusDisplayName(),
            'tracking_number' => $order->tracking_number,
            'courier_service' => $order->courier_service,
            'tracking_url' => $order->tracking_url,
            'status_history' => $order->status_history ?? [],
            'timestamps' => [
                'confirmed_at' => $order->confirmed_at,
                'processing_at' => $order->processing_at,
                'packed_at' => $order->packed_at,
                'shipped_at' => $order->shipped_at,
                'out_for_delivery_at' => $order->out_for_delivery_at,
                'delivered_at' => $order->delivered_at,
            ]
        ]);
    }
}
