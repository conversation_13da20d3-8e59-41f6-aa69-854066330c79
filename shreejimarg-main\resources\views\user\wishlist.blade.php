@extends('layouts.app')

@section('title', 'My Wishlist - ShreeJi Jewelry')
@section('description', 'Your saved jewelry items and favorites at ShreeJi.')

@section('content')
<!-- Page Header -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="font-playfair display-6 mb-2">My Wishlist</h1>
                <p class="text-muted mb-0">Your saved jewelry pieces</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                    <i class="fas fa-plus me-2"></i>Continue Shopping
                </a>
            </div>
        </div>
    </div>
</section>

@if($wishlists->count() > 0)
<!-- Wishlist Items -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            @foreach($wishlists as $wishlist)
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="card product-card border-0 shadow-sm h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="{{ $wishlist->product->main_image_url }}" alt="{{ $wishlist->product->name }}"
                             class="card-img-top product-image">
                        
                        @if($wishlist->product->isOnSale())
                            <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                {{ $wishlist->product->discount_percentage }}% OFF
                            </span>
                        @endif
                        
                        @if($wishlist->product->is_featured)
                            <span class="badge bg-primary-pink position-absolute top-0 end-0 m-2">
                                Featured
                            </span>
                        @endif
                        
                        <div class="product-overlay">
                            <div class="d-flex gap-2">
                                <button class="btn btn-danger btn-sm rounded-circle remove-wishlist-btn" 
                                        data-wishlist-id="{{ $wishlist->id }}"
                                        title="Remove from wishlist">
                                    <i class="fas fa-heart"></i>
                                </button>
                                <a href="{{ route('product.detail', $wishlist->product->slug) }}" 
                                   class="btn btn-light btn-sm rounded-circle"
                                   title="View details">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body p-3">
                        <div class="mb-2">
                            <small class="text-muted">{{ $wishlist->product->category->name }}</small>
                        </div>
                        <h6 class="card-title mb-2">
                            <a href="{{ route('product.detail', $wishlist->product->slug) }}" 
                               class="text-decoration-none text-dark">
                                {{ $wishlist->product->name }}
                            </a>
                        </h6>
                        <p class="card-text text-muted small mb-3">
                            {{ Str::limit($wishlist->product->short_description, 60) }}
                        </p>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="price">
                                @if($wishlist->product->isOnSale())
                                    <span class="text-primary-pink fw-bold">₹{{ number_format($wishlist->product->sale_price) }}</span>
                                    <small class="text-muted text-decoration-line-through ms-1">₹{{ number_format($wishlist->product->price) }}</small>
                                @else
                                    <span class="text-primary-pink fw-bold">₹{{ number_format($wishlist->product->price) }}</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            @if($wishlist->product->in_stock)
                                <button class="btn btn-primary-pink btn-sm add-to-cart-btn" 
                                        data-product-id="{{ $wishlist->product->id }}">
                                    <i class="fas fa-shopping-cart me-1"></i>Add to Cart
                                </button>
                            @else
                                <button class="btn btn-secondary btn-sm" disabled>
                                    <i class="fas fa-times me-1"></i>Out of Stock
                                </button>
                            @endif
                            <button class="btn btn-outline-danger btn-sm remove-wishlist-btn" 
                                    data-wishlist-id="{{ $wishlist->id }}">
                                <i class="fas fa-trash me-1"></i>Remove
                            </button>
                        </div>
                        
                        @if($wishlist->product->metal_type || $wishlist->product->stone_type)
                        <div class="mt-2">
                            @if($wishlist->product->metal_type)
                                <small class="badge bg-light text-dark me-1">{{ $wishlist->product->metal_type }}</small>
                            @endif
                            @if($wishlist->product->stone_type)
                                <small class="badge bg-light text-dark">{{ $wishlist->product->stone_type }}</small>
                            @endif
                        </div>
                        @endif
                        
                        <div class="mt-2">
                            <small class="text-muted">Added {{ $wishlist->created_at->diffForHumans() }}</small>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- Pagination -->
        @if($wishlists->hasPages())
        <div class="row mt-5">
            <div class="col-12">
                <nav aria-label="Wishlist pagination">
                    {{ $wishlists->links() }}
                </nav>
            </div>
        </div>
        @endif
        
        <!-- Wishlist Actions -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <button class="btn btn-outline-danger me-3" id="clearWishlistBtn">
                    <i class="fas fa-trash me-2"></i>Clear Wishlist
                </button>
                <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                    <i class="fas fa-shopping-bag me-2"></i>Continue Shopping
                </a>
            </div>
        </div>
    </div>
</section>

@else
<!-- Empty Wishlist -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <i class="fas fa-heart text-muted mb-4" style="font-size: 4rem;"></i>
                <h3 class="font-playfair mb-3">Your Wishlist is Empty</h3>
                <p class="text-muted mb-4">
                    Save your favorite jewelry pieces to your wishlist and never lose track of them.
                </p>
                <a href="{{ route('collections') }}" class="btn btn-primary-pink btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="font-playfair mb-4 text-center">You Might Like</h3>
        <div class="row g-4">
            @if(isset($featuredProducts))
                @foreach($featuredProducts->take(4) as $product)
                <div class="col-lg-3 col-md-6">
                    <div class="card product-card border-0 shadow-sm h-100">
                        <div class="position-relative overflow-hidden">
                            @if($product->images && count($product->images) > 0)
                                <img src="{{ $product->images[0] }}" alt="{{ $product->name }}" 
                                     class="card-img-top product-image">
                            @else
                                <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                                     alt="{{ $product->name }}" class="card-img-top product-image">
                            @endif
                            
                            <div class="product-overlay">
                                <div class="d-flex gap-2">
                                    <button class="btn btn-light btn-sm rounded-circle wishlist-btn" 
                                            data-product-id="{{ $product->id }}">
                                        <i class="far fa-heart"></i>
                                    </button>
                                    <a href="{{ route('product.detail', $product->slug) }}" 
                                       class="btn btn-light btn-sm rounded-circle">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">
                                <a href="{{ route('product.detail', $product->slug) }}" 
                                   class="text-decoration-none text-dark">
                                    {{ $product->name }}
                                </a>
                            </h6>
                            <div class="price">
                                <span class="text-primary-pink fw-bold">₹{{ number_format($product->current_price) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            @endif
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
    .product-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }
    
    .product-image {
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .product-image {
        transform: scale(1.05);
    }
    
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .product-card:hover .product-overlay {
        opacity: 1;
    }
</style>
@endpush

@push('scripts')
<script>
    // Remove from wishlist
    document.querySelectorAll('.remove-wishlist-btn').forEach(button => {
        button.addEventListener('click', function() {
            const wishlistId = this.dataset.wishlistId;
            
            if (confirm('Are you sure you want to remove this item from your wishlist?')) {
                fetch(`/wishlist/${wishlistId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error removing item from wishlist');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing item from wishlist');
                });
            }
        });
    });
    
    // Clear wishlist
    document.getElementById('clearWishlistBtn')?.addEventListener('click', function() {
        if (confirm('Are you sure you want to clear your entire wishlist?')) {
            fetch('/wishlist', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error clearing wishlist');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error clearing wishlist');
            });
        }
    });
    
    // Add to cart functionality
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            
            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: 1
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Product added to cart!');
                    // Update cart count if available
                    if (data.cart_count) {
                        document.querySelector('.cart-count')?.textContent = data.cart_count;
                    }
                } else {
                    alert(data.message || 'Error adding product to cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error adding product to cart');
            });
        });
    });
</script>
@endpush
