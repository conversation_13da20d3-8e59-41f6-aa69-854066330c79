<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Taskrouter\V1\Workspace;

use Twilio\Options;
use Twilio\Values;

abstract class TaskChannelOptions
{
    /**
     * @param bool $channelOptimizedRouting Whether the Task Channel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     * @return CreateTaskChannelOptions Options builder
     */
    public static function create(
        
        bool $channelOptimizedRouting = Values::BOOL_NONE

    ): CreateTaskChannelOptions
    {
        return new CreateTaskChannelOptions(
            $channelOptimizedRouting
        );
    }




    /**
     * @param string $friendlyName A descriptive string that you create to describe the Task Channel. It can be up to 64 characters long.
     * @param bool $channelOptimizedRouting Whether the TaskChannel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     * @return UpdateTaskChannelOptions Options builder
     */
    public static function update(
        
        string $friendlyName = Values::NONE,
        bool $channelOptimizedRouting = Values::BOOL_NONE

    ): UpdateTaskChannelOptions
    {
        return new UpdateTaskChannelOptions(
            $friendlyName,
            $channelOptimizedRouting
        );
    }

}

class CreateTaskChannelOptions extends Options
    {
    /**
     * @param bool $channelOptimizedRouting Whether the Task Channel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     */
    public function __construct(
        
        bool $channelOptimizedRouting = Values::BOOL_NONE

    ) {
        $this->options['channelOptimizedRouting'] = $channelOptimizedRouting;
    }

    /**
     * Whether the Task Channel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     *
     * @param bool $channelOptimizedRouting Whether the Task Channel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     * @return $this Fluent Builder
     */
    public function setChannelOptimizedRouting(bool $channelOptimizedRouting): self
    {
        $this->options['channelOptimizedRouting'] = $channelOptimizedRouting;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.CreateTaskChannelOptions ' . $options . ']';
    }
}




class UpdateTaskChannelOptions extends Options
    {
    /**
     * @param string $friendlyName A descriptive string that you create to describe the Task Channel. It can be up to 64 characters long.
     * @param bool $channelOptimizedRouting Whether the TaskChannel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     */
    public function __construct(
        
        string $friendlyName = Values::NONE,
        bool $channelOptimizedRouting = Values::BOOL_NONE

    ) {
        $this->options['friendlyName'] = $friendlyName;
        $this->options['channelOptimizedRouting'] = $channelOptimizedRouting;
    }

    /**
     * A descriptive string that you create to describe the Task Channel. It can be up to 64 characters long.
     *
     * @param string $friendlyName A descriptive string that you create to describe the Task Channel. It can be up to 64 characters long.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Whether the TaskChannel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     *
     * @param bool $channelOptimizedRouting Whether the TaskChannel should prioritize Workers that have been idle. If `true`, Workers that have been idle the longest are prioritized.
     * @return $this Fluent Builder
     */
    public function setChannelOptimizedRouting(bool $channelOptimizedRouting): self
    {
        $this->options['channelOptimizedRouting'] = $channelOptimizedRouting;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Taskrouter.V1.UpdateTaskChannelOptions ' . $options . ']';
    }
}

