<?php

namespace App\Providers;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class ShreeJiServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share categories with all views
        View::composer('*', function ($view) {
            $categories = Category::active()->ordered()->get();
            $view->with('globalCategories', $categories);
        });

        // Share featured products and categories with homepage
        View::composer('welcome', function ($view) {
            $featuredProducts = Product::with('category')
                ->featured()
                ->active()
                ->inStock()
                ->limit(8)
                ->get();

            $featuredCategories = Category::active()
                ->featured()
                ->ordered()
                ->limit(4)
                ->get();

            $view->with('featuredProducts', $featuredProducts);
            $view->with('featuredCategories', $featuredCategories);
        });
    }
}
