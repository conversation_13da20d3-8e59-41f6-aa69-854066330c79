<?php

namespace App\Http\Controllers;

use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $wishlists = Auth::user()->wishlists()
            ->with('product.category')
            ->latest()
            ->paginate(12);

        // Get featured products for suggestions
        $featuredProducts = \App\Models\Product::with('category')
            ->featured()
            ->active()
            ->inStock()
            ->limit(4)
            ->get();

        return view('user.wishlist', compact('wishlists', 'featuredProducts'));
    }

    public function toggle(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $userId = Auth::id();
        $productId = $request->product_id;

        $isAdded = Wishlist::toggle($userId, $productId);

        return response()->json([
            'success' => true,
            'is_added' => $isAdded,
            'message' => $isAdded ? 'Added to wishlist!' : 'Removed from wishlist!'
        ]);
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $userId = Auth::id();
        $productId = $request->product_id;

        if (Wishlist::isInWishlist($userId, $productId)) {
            return response()->json([
                'success' => false,
                'message' => 'Product is already in your wishlist!'
            ], 400);
        }

        Wishlist::create([
            'user_id' => $userId,
            'product_id' => $productId
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Product added to wishlist!'
        ]);
    }

    public function remove($id)
    {
        $wishlist = Auth::user()->wishlists()->findOrFail($id);
        $wishlist->delete();

        return response()->json([
            'success' => true,
            'message' => 'Product removed from wishlist!'
        ]);
    }

    public function clear()
    {
        Auth::user()->wishlists()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Wishlist cleared successfully!'
        ]);
    }

    public function check(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $isInWishlist = Wishlist::isInWishlist(Auth::id(), $request->product_id);

        return response()->json([
            'is_in_wishlist' => $isInWishlist
        ]);
    }
}
