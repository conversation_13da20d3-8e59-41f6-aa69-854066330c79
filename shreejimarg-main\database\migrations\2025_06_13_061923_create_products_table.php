<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('sku')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->decimal('price', 10, 2);
            $table->decimal('sale_price', 10, 2)->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->boolean('manage_stock')->default(true);
            $table->boolean('in_stock')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->enum('status', ['active', 'inactive', 'draft'])->default('active');
            $table->json('images')->nullable();
            $table->json('specifications')->nullable(); // Metal type, stone details, etc.
            $table->json('sizes')->nullable(); // Available sizes for rings, etc.
            $table->decimal('weight', 8, 2)->nullable(); // Weight in grams
            $table->string('metal_type')->nullable(); // Gold, Silver, Platinum
            $table->string('metal_purity')->nullable(); // 18K, 22K, etc.
            $table->string('stone_type')->nullable(); // Diamond, Pearl, etc.
            $table->decimal('stone_weight', 8, 2)->nullable(); // Carat weight
            $table->string('certification')->nullable(); // GIA, etc.
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
