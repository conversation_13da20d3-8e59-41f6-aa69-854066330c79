<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Voice
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Voice\V1;

use Twilio\ListResource;
use Twilio\Version;


class ArchivedCallList extends ListResource
    {
    /**
     * Construct the ArchivedCallList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];
    }

    /**
     * Constructs a ArchivedCallContext
     *
     * @param \DateTime $date The date of the Call in UTC.
     *
     * @param string $sid The Twilio-provided Call SID that uniquely identifies the Call resource to delete
     */
    public function getContext(
        \DateTime $date
        , string $sid
        
    ): ArchivedCallContext
    {
        return new ArchivedCallContext(
            $this->version,
            $date,
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Voice.V1.ArchivedCallList]';
    }
}
