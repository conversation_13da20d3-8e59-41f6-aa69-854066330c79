<?php

namespace Database\Seeders;

use App\Models\Review;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::limit(5)->get();
        $users = User::limit(10)->get();

        if ($products->isEmpty() || $users->isEmpty()) {
            $this->command->info('No products or users found. Please seed products and users first.');
            return;
        }

        $sampleReviews = [
            [
                'rating' => 5,
                'title' => 'Absolutely stunning!',
                'comment' => 'This jewelry piece exceeded my expectations. The craftsmanship is exceptional and it looks even better in person. Highly recommend!',
                'is_verified_purchase' => true,
            ],
            [
                'rating' => 5,
                'title' => 'Perfect for special occasions',
                'comment' => 'Bought this for my anniversary and my wife loves it. The quality is top-notch and the packaging was beautiful.',
                'is_verified_purchase' => true,
            ],
            [
                'rating' => 4,
                'title' => 'Great quality',
                'comment' => 'Very happy with this purchase. The design is elegant and the material feels premium. Fast delivery too!',
                'is_verified_purchase' => false,
            ],
            [
                'rating' => 5,
                'title' => 'Excellent service',
                'comment' => 'Not only is the product beautiful, but the customer service was outstanding. They helped me choose the perfect size.',
                'is_verified_purchase' => true,
            ],
            [
                'rating' => 4,
                'title' => 'Beautiful design',
                'comment' => 'The design is exactly what I was looking for. Good value for money and arrived quickly.',
                'is_verified_purchase' => false,
            ],
            [
                'rating' => 5,
                'title' => 'Perfect gift',
                'comment' => 'Bought this as a gift and the recipient was thrilled. The quality and presentation were perfect.',
                'is_verified_purchase' => true,
            ],
        ];

        foreach ($products as $product) {
            // Add 2-4 reviews per product
            $reviewCount = rand(2, 4);
            $usedUsers = [];

            for ($i = 0; $i < $reviewCount; $i++) {
                // Get a random user that hasn't reviewed this product yet
                do {
                    $user = $users->random();
                } while (in_array($user->id, $usedUsers));

                $usedUsers[] = $user->id;
                $reviewData = $sampleReviews[array_rand($sampleReviews)];

                Review::create([
                    'product_id' => $product->id,
                    'user_id' => $user->id,
                    'rating' => $reviewData['rating'],
                    'title' => $reviewData['title'],
                    'comment' => $reviewData['comment'],
                    'is_verified_purchase' => $reviewData['is_verified_purchase'],
                    'is_approved' => true,
                    'helpful_count' => rand(0, 15),
                    'created_at' => now()->subDays(rand(1, 60)),
                ]);
            }
        }

        $this->command->info('Sample reviews created successfully!');
    }
}
