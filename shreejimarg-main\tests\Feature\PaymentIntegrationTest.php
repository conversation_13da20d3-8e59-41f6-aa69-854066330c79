<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\Category;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;

class PaymentIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $order;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        // Create test category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::create([
            'category_id' => $category->id,
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-001',
            'description' => 'Test product description',
            'price' => 100.00,
            'stock_quantity' => 10,
            'in_stock' => true,
            'status' => 'active',
            'images' => ['test.jpg'],
            'specifications' => ['test' => 'value'],
            'sizes' => ['M'],
        ]);

        // Create test order
        $this->order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST_' . time(),
            'subtotal' => 100.00,
            'tax_amount' => 0.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'razorpay',
            'shipping_address' => [
                'name' => 'Test User',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456'
            ],
            'billing_address' => [
                'name' => 'Test User',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456'
            ]
        ]);

        // Create order item
        OrderItem::create([
            'order_id' => $this->order->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'product_sku' => $this->product->sku,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00
        ]);
    }

    /** @test */
    public function it_can_access_payment_test_details_in_local_environment()
    {
        config(['app.env' => 'local']);

        $response = $this->get('/payment/test-details');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success_cards' => [
                    '*' => ['number', 'cvv', 'expiry', 'name']
                ],
                'failure_cards',
                'test_upi_id',
                'test_netbanking'
            ]);
    }

    /** @test */
    public function it_denies_payment_test_details_in_production()
    {
        config(['app.env' => 'production']);

        $response = $this->get('/payment/test-details');

        $response->assertStatus(403)
            ->assertJson([
                'error' => 'Not available in production'
            ]);
    }

    /** @test */
    public function it_requires_authentication_for_payment_operations()
    {
        $response = $this->postJson('/payment/create-order', [
            'order_id' => $this->order->id
        ]);

        $response->assertStatus(401);

        $response = $this->postJson('/payment/verify', [
            'razorpay_order_id' => 'order_test_123',
            'razorpay_payment_id' => 'pay_test_123',
            'razorpay_signature' => 'signature_test_123',
            'order_id' => $this->order->id
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_validates_required_fields_for_payment_verification()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/payment/verify', [
                'order_id' => $this->order->id
                // Missing required fields
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'razorpay_order_id',
                'razorpay_payment_id',
                'razorpay_signature'
            ]);
    }

    /** @test */
    public function it_fails_to_create_payment_order_for_nonexistent_order()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => 99999
            ]);

        $response->assertStatus(404);
    }

    /** @test */
    public function it_fails_to_create_payment_order_for_already_paid_order()
    {
        // Mark order as paid
        $this->order->update(['payment_status' => 'paid']);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Order is already paid'
            ]);
    }

    /** @test */
    public function it_prevents_unauthorized_access_to_orders()
    {
        // Create another user
        $otherUser = User::create([
            'name' => 'Other User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        $response = $this->actingAs($otherUser)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Unauthorized access to order'
            ]);
    }

    /** @test */
    public function it_can_handle_payment_failure()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/payment/failed', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => false,
                'message' => 'Payment failed. Your cart has been restored.'
            ]);

        // Verify order status updated
        $this->order->refresh();
        $this->assertEquals('failed', $this->order->payment_status);
        $this->assertEquals('cancelled', $this->order->status);
    }

    /** @test */
    public function it_can_process_webhook_with_valid_signature()
    {
        // This test would require actual webhook signature validation
        // For now, we'll test the endpoint exists and handles invalid signatures
        $response = $this->postJson('/payment/webhook', [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test_123',
                        'order_id' => 'order_test_123',
                        'amount' => 10000,
                        'currency' => 'INR',
                        'status' => 'captured'
                    ]
                ]
            ]
        ], [
            'X-Razorpay-Signature' => 'invalid_signature'
        ]);

        // Should return 400 for invalid signature
        $response->assertStatus(400);
    }

    /** @test */
    public function order_model_has_required_relationships()
    {
        $this->assertInstanceOf(User::class, $this->order->user);
        $this->assertCount(1, $this->order->orderItems);
        $this->assertInstanceOf(Product::class, $this->order->orderItems->first()->product);
    }

    /** @test */
    public function order_model_has_correct_attributes()
    {
        $this->assertEquals('pending', $this->order->status);
        $this->assertEquals('pending', $this->order->payment_status);
        $this->assertEquals(100.00, $this->order->total_amount);
        $this->assertEquals('INR', $this->order->currency);
        $this->assertEquals('razorpay', $this->order->payment_method);
    }

    /** @test */
    public function order_can_generate_unique_order_numbers()
    {
        // Create a dummy order first to establish a baseline
        Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'ORD-' . date('Y') . '-001',
            'subtotal' => 50.00,
            'tax_amount' => 0.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 50.00,
            'currency' => 'INR',
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'cod',
            'shipping_address' => ['name' => 'Test'],
            'billing_address' => ['name' => 'Test']
        ]);

        $orderNumber1 = Order::generateOrderNumber();

        // Create an order with the first number to update the sequence
        Order::create([
            'user_id' => $this->user->id,
            'order_number' => $orderNumber1,
            'subtotal' => 75.00,
            'tax_amount' => 0.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 75.00,
            'currency' => 'INR',
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'cod',
            'shipping_address' => ['name' => 'Test'],
            'billing_address' => ['name' => 'Test']
        ]);

        $orderNumber2 = Order::generateOrderNumber();

        $this->assertNotEquals($orderNumber1, $orderNumber2);
        $this->assertStringStartsWith('ORD-' . date('Y') . '-', $orderNumber1);
        $this->assertStringStartsWith('ORD-' . date('Y') . '-', $orderNumber2);
        $this->assertEquals('ORD-' . date('Y') . '-002', $orderNumber1);
        $this->assertEquals('ORD-' . date('Y') . '-003', $orderNumber2);
    }
}
