<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Tracking information
            $table->string('tracking_number')->nullable()->after('shipping_method');
            $table->string('courier_service')->default('India Post')->after('tracking_number');
            $table->string('tracking_url')->nullable()->after('courier_service');

            // Status tracking timestamps
            $table->timestamp('confirmed_at')->nullable()->after('delivered_at');
            $table->timestamp('processing_at')->nullable()->after('confirmed_at');
            $table->timestamp('packed_at')->nullable()->after('processing_at');
            $table->timestamp('out_for_delivery_at')->nullable()->after('packed_at');

            // Admin tracking fields
            $table->text('admin_notes')->nullable()->after('notes');
            $table->string('updated_by')->nullable()->after('admin_notes'); // Admin who last updated
            $table->json('status_history')->nullable()->after('updated_by'); // Track all status changes

            // Delivery confirmation
            $table->string('delivery_confirmation_method')->nullable()->after('status_history'); // SMS, Email, Call
            $table->string('delivered_to')->nullable()->after('delivery_confirmation_method'); // Person who received
            $table->text('delivery_notes')->nullable()->after('delivered_to');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'tracking_number',
                'courier_service',
                'tracking_url',
                'confirmed_at',
                'processing_at',
                'packed_at',
                'out_for_delivery_at',
                'admin_notes',
                'updated_by',
                'status_history',
                'delivery_confirmation_method',
                'delivered_to',
                'delivery_notes'
            ]);
        });
    }
};
