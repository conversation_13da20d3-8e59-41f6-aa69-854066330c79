@props(['style' => 'default', 'showSocial' => true, 'showAddress' => true])

@php
$classes = match($style) {
    'inline' => 'd-flex flex-wrap gap-3 align-items-center',
    'vertical' => 'd-flex flex-column gap-2',
    'horizontal' => 'd-flex flex-wrap gap-4 justify-content-center',
    default => 'contact-info'
};
@endphp

<div class="{{ $classes }}" {{ $attributes }}>
    <!-- Phone -->
    <div class="contact-item">
        <i class="fas fa-phone text-primary-pink me-2"></i>
        <a href="tel:{{ $contact['phoneLink'] }}" class="text-decoration-none">
            {{ $contact['phoneFormatted'] }}
        </a>
    </div>

    <!-- Email -->
    <div class="contact-item">
        <i class="fas fa-envelope text-primary-pink me-2"></i>
        <a href="mailto:{{ $contact['email'] }}" class="text-decoration-none">
            {{ $contact['email'] }}
        </a>
    </div>

    <!-- WhatsApp -->
    <div class="contact-item">
        <i class="fab fa-whatsapp text-success me-2"></i>
        <a href="{{ $contact['whatsappLink'] }}" target="_blank" class="text-decoration-none">
            WhatsApp
        </a>
    </div>

    @if($showAddress)
    <!-- Address -->
    <div class="contact-item">
        <i class="fas fa-map-marker-alt text-primary-pink me-2"></i>
        <span>{{ $contact['address'] }}</span>
    </div>
    @endif

    @if($showSocial)
    <!-- Social Media -->
    <div class="contact-item">
        <div class="d-flex gap-2">
            @foreach($contact['social'] as $platform => $details)
            <a href="{{ $details['url'] }}" target="_blank" 
               class="btn btn-sm btn-outline-primary-pink rounded-circle" 
               title="{{ ucfirst($platform) }}">
                <i class="fab fa-{{ $platform === 'facebook' ? 'facebook-f' : $platform }}"></i>
            </a>
            @endforeach
        </div>
    </div>
    @endif
</div>
