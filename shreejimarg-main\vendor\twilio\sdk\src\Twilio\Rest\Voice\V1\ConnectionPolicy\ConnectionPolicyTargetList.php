<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Voice
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Voice\V1\ConnectionPolicy;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Stream;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class ConnectionPolicyTargetList extends ListResource
    {
    /**
     * Construct the ConnectionPolicyTargetList
     *
     * @param Version $version Version that contains the resource
     * @param string $connectionPolicySid The SID of the Connection Policy that owns the Target.
     */
    public function __construct(
        Version $version,
        string $connectionPolicySid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'connectionPolicySid' =>
            $connectionPolicySid,
        
        ];

        $this->uri = '/ConnectionPolicies/' . \rawurlencode($connectionPolicySid)
        .'/Targets';
    }

    /**
     * Create the ConnectionPolicyTargetInstance
     *
     * @param string $target The SIP address you want Twilio to route your calls to. This must be a `sip:` schema. `sips` is NOT supported.
     * @param array|Options $options Optional Arguments
     * @return ConnectionPolicyTargetInstance Created ConnectionPolicyTargetInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $target, array $options = []): ConnectionPolicyTargetInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'Target' =>
                $target,
            'FriendlyName' =>
                $options['friendlyName'],
            'Priority' =>
                $options['priority'],
            'Weight' =>
                $options['weight'],
            'Enabled' =>
                Serialize::booleanToString($options['enabled']),
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new ConnectionPolicyTargetInstance(
            $this->version,
            $payload,
            $this->solution['connectionPolicySid']
        );
    }


    /**
     * Reads ConnectionPolicyTargetInstance records from the API as a list.
     * Unlike stream(), this operation is eager and will load `limit` records into
     * memory before returning.
     *
     * @param int $limit Upper limit for the number of records to return. read()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, read()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return ConnectionPolicyTargetInstance[] Array of results
     */
    public function read(?int $limit = null, $pageSize = null): array
    {
        return \iterator_to_array($this->stream($limit, $pageSize), false);
    }

    /**
     * Streams ConnectionPolicyTargetInstance records from the API as a generator stream.
     * This operation lazily loads records as efficiently as possible until the
     * limit
     * is reached.
     * The results are returned as a generator, so this operation is memory
     * efficient.
     *
     * @param int $limit Upper limit for the number of records to return. stream()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, stream()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return Stream stream of results
     */
    public function stream(?int $limit = null, $pageSize = null): Stream
    {
        $limits = $this->version->readLimits($limit, $pageSize);

        $page = $this->page($limits['pageSize']);

        return $this->version->stream($page, $limits['limit'], $limits['pageLimit']);
    }

    /**
     * Retrieve a single page of ConnectionPolicyTargetInstance records from the API.
     * Request is executed immediately
     *
     * @param mixed $pageSize Number of records to return, defaults to 50
     * @param string $pageToken PageToken provided by the API
     * @param mixed $pageNumber Page Number, this value is simply for client state
     * @return ConnectionPolicyTargetPage Page of ConnectionPolicyTargetInstance
     */
    public function page(
        $pageSize = Values::NONE,
        string $pageToken = Values::NONE,
        $pageNumber = Values::NONE
    ): ConnectionPolicyTargetPage
    {

        $params = Values::of([
            'PageToken' => $pageToken,
            'Page' => $pageNumber,
            'PageSize' => $pageSize,
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json']);
        $response = $this->version->page('GET', $this->uri, $params, [], $headers);

        return new ConnectionPolicyTargetPage($this->version, $response, $this->solution);
    }

    /**
     * Retrieve a specific page of ConnectionPolicyTargetInstance records from the API.
     * Request is executed immediately
     *
     * @param string $targetUrl API-generated URL for the requested results page
     * @return ConnectionPolicyTargetPage Page of ConnectionPolicyTargetInstance
     */
    public function getPage(string $targetUrl): ConnectionPolicyTargetPage
    {
        $response = $this->version->getDomain()->getClient()->request(
            'GET',
            $targetUrl
        );

        return new ConnectionPolicyTargetPage($this->version, $response, $this->solution);
    }


    /**
     * Constructs a ConnectionPolicyTargetContext
     *
     * @param string $sid The unique string that we created to identify the Target resource to delete.
     */
    public function getContext(
        string $sid
        
    ): ConnectionPolicyTargetContext
    {
        return new ConnectionPolicyTargetContext(
            $this->version,
            $this->solution['connectionPolicySid'],
            $sid
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Voice.V1.ConnectionPolicyTargetList]';
    }
}
