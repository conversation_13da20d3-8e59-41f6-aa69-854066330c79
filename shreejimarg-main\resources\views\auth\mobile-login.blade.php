@extends('layouts.app')

@section('title', 'Login with Mobile - ShreeJi Jewelry')
@section('description', 'Login to your ShreeJi account using your mobile number for quick and secure access.')

@section('content')
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <h2 class="font-playfair text-primary-pink mb-2">
                                <i class="fas fa-gem me-2"></i>ShreeJi
                            </h2>
                            <h4 class="font-playfair mb-3">Welcome Back</h4>
                            <p class="text-muted">Enter your mobile number to continue</p>
                        </div>
                        
                        <!-- Mobile Login Form -->
                        <form id="mobileLoginForm">
                            @csrf
                            <div class="mb-4">
                                <label for="phone" class="form-label">Mobile Number</label>
                                <div class="input-group">
                                    <span class="input-group-text">+91</span>
                                    <input type="tel" class="form-control form-control-lg" id="phone" name="phone" 
                                           placeholder="Enter 10-digit mobile number" maxlength="10" required>
                                </div>
                                <div class="invalid-feedback" id="phoneError"></div>
                                <small class="text-muted">We'll send you a 6-digit OTP to verify your number</small>
                            </div>

                            <button type="submit" class="btn btn-primary-pink w-100 btn-lg mb-3" id="sendOtpBtn">
                                <i class="fas fa-mobile-alt me-2"></i>Send OTP
                            </button>
                        </form>
                        
                        <!-- Alternative Login -->
                        <div class="text-center mb-4">
                            <p class="text-muted mb-3">Or continue with</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="{{ route('login') }}" class="btn btn-outline-secondary flex-fill">
                                    <i class="fas fa-envelope me-2"></i>Email
                                </a>
                                <button class="btn btn-outline-success flex-fill" onclick="continueAsGuest()">
                                    <i class="fas fa-user me-2"></i>Guest
                                </button>
                            </div>
                        </div>
                        
                        <!-- Register Link -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                New to ShreeJi? 
                                <a href="{{ route('register') }}" class="text-primary-pink text-decoration-none fw-semibold">Create Account</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Format phone number input
    document.getElementById('phone').addEventListener('input', function(e) {
        // Remove non-digits
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 10 digits
        if (value.length > 10) {
            value = value.slice(0, 10);
        }
        
        e.target.value = value;
        
        // Clear previous errors
        clearErrors();
    });

    // Handle form submission
    document.getElementById('mobileLoginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const sendOtpBtn = document.getElementById('sendOtpBtn');
        const phone = document.getElementById('phone').value;

        // Validate phone number
        if (!/^[0-9]{10}$/.test(phone)) {
            showError('phone', 'Please enter a valid 10-digit mobile number');
            return;
        }

        // Clear previous errors
        clearErrors();

        // Show loading state
        sendOtpBtn.disabled = true;
        sendOtpBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending OTP...';

        fetch('{{ route("mobile.send-otp") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to OTP verification page
                window.location.href = '{{ route("mobile.verify") }}';
            } else {
                showError('phone', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('phone', 'Failed to send OTP. Please try again.');
        })
        .finally(() => {
            // Reset button state
            sendOtpBtn.disabled = false;
            sendOtpBtn.innerHTML = '<i class="fas fa-mobile-alt me-2"></i>Send OTP';
        });
    });

    function showError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + 'Error');
        
        field.classList.add('is-invalid');
        errorDiv.textContent = message;
    }

    function clearErrors() {
        const fields = ['phone'];
        fields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + 'Error');
            
            field.classList.remove('is-invalid');
            errorDiv.textContent = '';
        });
    }

    function continueAsGuest() {
        // Check if there are items in cart
        fetch('{{ route("api.cart.count") }}')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                // Redirect to guest checkout
                window.location.href = '{{ route("checkout.guest") }}';
            } else {
                alert('Your cart is empty. Please add some items before checkout.');
                window.location.href = '{{ route("products") }}';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.location.href = '{{ route("products") }}';
        });
    }
</script>
@endpush
