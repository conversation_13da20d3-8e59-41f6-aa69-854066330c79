@extends('layouts.app')

@section('title', 'Login with Mobile - ShreeJi Jewelry')
@section('description', 'Login to your ShreeJi account using your mobile number for quick and secure access.')

@section('content')
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <h2 class="font-playfair text-primary-pink mb-2">
                                <i class="fas fa-gem me-2"></i>ShreeJi
                            </h2>
                            <h4 class="font-playfair mb-3">Welcome Back</h4>
                            <p class="text-muted">Enter your mobile number to continue</p>
                        </div>
                        
                        <!-- Mobile Login Section -->
                        <div id="mobileLoginSection">
                            <!-- Mobile Login Form -->
                            <form id="mobileLoginForm">
                                @csrf
                                <div class="mb-3">
                                    <label for="name" class="form-label">Your Name</label>
                                    <input type="text" class="form-control form-control-lg" id="name" name="name"
                                           placeholder="Enter your full name" required>
                                    <div class="invalid-feedback" id="nameError"></div>
                                </div>

                                <div class="mb-4">
                                    <label for="phone" class="form-label">Mobile Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text">+91</span>
                                        <input type="tel" class="form-control form-control-lg" id="phone" name="phone"
                                               placeholder="Enter 10-digit mobile number" maxlength="10" required>
                                    </div>
                                    <div class="invalid-feedback" id="phoneError"></div>
                                    <small class="text-muted">We'll send you a 6-digit OTP to verify your number</small>
                                </div>

                                <button type="submit" class="btn btn-primary-pink w-100 btn-lg mb-3" id="sendOtpBtn">
                                    <i class="fas fa-mobile-alt me-2"></i>Send OTP
                                </button>
                            </form>

                            <!-- Alternative Options -->
                            <div class="text-center mb-4">
                                <p class="text-muted mb-3">Or</p>
                                <div class="d-flex gap-2 mb-3">
                                    <button class="btn btn-outline-secondary flex-fill" onclick="showEmailLogin()" type="button">
                                        <i class="fas fa-envelope me-2"></i>Email Login
                                    </button>
                                    <button class="btn btn-outline-success flex-fill" onclick="continueAsGuest()" type="button">
                                        <i class="fas fa-shopping-cart me-2"></i>Guest
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Email Login Form (Hidden by default) -->
                        <div id="emailLoginForm" class="d-none">
                            <form id="emailForm">
                                @csrf
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control form-control-lg" id="email" name="email"
                                           placeholder="Enter your email address" required>
                                    <div class="invalid-feedback" id="emailError"></div>
                                </div>

                                <div class="mb-4">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" name="password"
                                               placeholder="Enter your password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback" id="passwordError"></div>
                                </div>

                                <button type="submit" class="btn btn-primary-pink w-100 btn-lg mb-3" id="emailLoginBtn">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login with Email
                                </button>
                            </form>

                            <div class="text-center mb-3">
                                <button class="btn btn-link text-muted" onclick="showMobileLogin()" type="button">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Mobile Login
                                </button>
                            </div>
                        </div>

                        <!-- Register Link -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                New to ShreeJi?
                                <a href="{{ route('register') }}" class="text-primary-pink text-decoration-none fw-semibold">Create Account</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Format phone number input
    document.getElementById('phone').addEventListener('input', function(e) {
        // Remove non-digits
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 10 digits
        if (value.length > 10) {
            value = value.slice(0, 10);
        }
        
        e.target.value = value;
        
        // Clear previous errors
        clearErrors();
    });

    // Handle form submission
    document.getElementById('mobileLoginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const sendOtpBtn = document.getElementById('sendOtpBtn');
        const phone = document.getElementById('phone').value;
        const name = document.getElementById('name').value.trim();

        // Validate name
        if (!name || name.length < 2) {
            showError('name', 'Please enter your full name');
            return;
        }

        // Validate phone number
        if (!/^[0-9]{10}$/.test(phone)) {
            showError('phone', 'Please enter a valid 10-digit mobile number');
            return;
        }

        // Clear previous errors
        clearErrors();

        // Show loading state
        sendOtpBtn.disabled = true;
        sendOtpBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending OTP...';

        fetch('{{ route("mobile.send-otp") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store name in session storage for OTP verification
                sessionStorage.setItem('user_name', name);
                // Redirect to OTP verification page
                window.location.href = '{{ route("mobile.verify") }}';
            } else {
                showError('phone', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('phone', 'Failed to send OTP. Please try again.');
        })
        .finally(() => {
            // Reset button state
            sendOtpBtn.disabled = false;
            sendOtpBtn.innerHTML = '<i class="fas fa-mobile-alt me-2"></i>Send OTP';
        });
    });

    function showError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + 'Error');
        
        field.classList.add('is-invalid');
        errorDiv.textContent = message;
    }

    function clearErrors() {
        const fields = ['name', 'phone', 'email', 'password'];
        fields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + 'Error');

            if (field) field.classList.remove('is-invalid');
            if (errorDiv) errorDiv.textContent = '';
        });
    }

    function continueAsGuest() {
        // Check if there are items in cart
        fetch('{{ route("api.cart.count") }}')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                // Redirect to guest checkout
                window.location.href = '{{ route("checkout.guest") }}';
            } else {
                alert('Your cart is empty. Please add some items before checkout.');
                window.location.href = '{{ route("products") }}';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            window.location.href = '{{ route("products") }}';
        });
    }

    function showEmailLogin() {
        try {
            document.getElementById('mobileLoginSection').style.display = 'none';
            document.getElementById('emailLoginForm').style.display = 'block';
            clearErrors();
        } catch (error) {
            console.error('Error showing email login:', error);
        }
    }

    function showMobileLogin() {
        try {
            document.getElementById('emailLoginForm').style.display = 'none';
            document.getElementById('mobileLoginSection').style.display = 'block';
            clearErrors();
        } catch (error) {
            console.error('Error showing mobile login:', error);
        }
    }

    function togglePassword() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggleIcon');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Email login form submission - wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        const emailForm = document.getElementById('emailForm');
        if (!emailForm) {
            console.error('Email form not found!');
            return;
        }

        emailForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const emailLoginBtn = document.getElementById('emailLoginBtn');
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        // Validate email
        if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            showError('email', 'Please enter a valid email address');
            return;
        }

        // Validate password
        if (!password || password.length < 6) {
            showError('password', 'Password must be at least 6 characters');
            return;
        }

        // Clear previous errors
        clearErrors();

        // Update button state
        emailLoginBtn.disabled = true;
        emailLoginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';

        // Send login request
        fetch('{{ route("email.login.post") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to dashboard or intended page
                window.location.href = data.redirect || '{{ route("dashboard") }}';
            } else {
                if (data.errors) {
                    // Show field-specific errors
                    Object.keys(data.errors).forEach(field => {
                        showError(field, data.errors[field][0]);
                    });
                } else {
                    showError('email', data.message || 'Invalid email or password');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('email', 'Login failed. Please try again.');
        })
        .finally(() => {
            // Reset button state
            emailLoginBtn.disabled = false;
            emailLoginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Login with Email';
        });
        }); // End of email form event listener
    }); // End of DOMContentLoaded
</script>
@endpush
