<?php $__env->startSection('title', 'Verify OTP - ShreeJi Jewelry'); ?>
<?php $__env->startSection('description', 'Enter the OTP sent to your mobile number to complete verification.'); ?>

<?php $__env->startSection('content'); ?>
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <h2 class="font-playfair text-primary-pink mb-2">
                                <i class="fas fa-gem me-2"></i>ShreeJi
                            </h2>
                            <h4 class="font-playfair mb-3">Verify Your Number</h4>
                            <p class="text-muted">
                                We've sent a 6-digit OTP to<br>
                                <strong>+91-<?php echo e(substr($phone, 0, 5)); ?>-<?php echo e(substr($phone, 5)); ?></strong>
                            </p>
                        </div>
                        
                        <!-- OTP Verification Form -->
                        <form id="otpVerificationForm">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="phone" value="<?php echo e($phone); ?>">
                            <input type="hidden" name="purpose" value="<?php echo e($purpose); ?>">
                            
                            <div class="mb-4">
                                <label for="otp_code" class="form-label">Enter OTP</label>
                                <input type="text" class="form-control form-control-lg text-center" 
                                       id="otp_code" name="otp_code" placeholder="000000" 
                                       maxlength="6" required autocomplete="one-time-code">
                                <div class="invalid-feedback" id="otpError"></div>
                            </div>

                            <!-- Name field for new users (shown conditionally) -->
                            <div class="mb-4 d-none" id="nameField">
                                <label for="name" class="form-label">Your Name</label>
                                <input type="text" class="form-control form-control-lg" 
                                       id="name" name="name" placeholder="Enter your full name">
                                <div class="invalid-feedback" id="nameError"></div>
                            </div>

                            <button type="submit" class="btn btn-primary-pink w-100 btn-lg mb-3" id="verifyBtn">
                                <i class="fas fa-check me-2"></i>Verify & Continue
                            </button>
                        </form>
                        
                        <!-- Resend OTP -->
                        <div class="text-center mb-4">
                            <p class="text-muted mb-2">
                                Didn't receive the code?
                            </p>
                            <button class="btn btn-link text-primary-pink p-0" id="resendBtn" onclick="resendOtp()">
                                Resend OTP
                            </button>
                            <div id="resendTimer" class="text-muted small mt-1"></div>
                        </div>
                        
                        <!-- Back Link -->
                        <div class="text-center">
                            <a href="<?php echo e(route('mobile.login')); ?>" class="text-muted text-decoration-none">
                                <i class="fas fa-arrow-left me-2"></i>Change Mobile Number
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    let resendTimer = 60; // 60 seconds cooldown
    let timerInterval;

    // Start resend timer
    function startResendTimer() {
        const resendBtn = document.getElementById('resendBtn');
        const timerDiv = document.getElementById('resendTimer');
        
        resendBtn.disabled = true;
        resendBtn.textContent = 'Resend OTP';
        
        timerInterval = setInterval(() => {
            resendTimer--;
            timerDiv.textContent = `Resend available in ${resendTimer}s`;
            
            if (resendTimer <= 0) {
                clearInterval(timerInterval);
                resendBtn.disabled = false;
                timerDiv.textContent = '';
                resendTimer = 60; // Reset for next time
            }
        }, 1000);
    }

    // Start timer on page load
    startResendTimer();

    // Format OTP input
    document.getElementById('otp_code').addEventListener('input', function(e) {
        // Remove non-digits
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 6 digits
        if (value.length > 6) {
            value = value.slice(0, 6);
        }
        
        e.target.value = value;
        
        // Clear previous errors
        clearErrors();
        
        // Auto-submit when 6 digits entered
        if (value.length === 6) {
            setTimeout(() => {
                document.getElementById('otpVerificationForm').dispatchEvent(new Event('submit'));
            }, 500);
        }
    });

    // Handle form submission
    document.getElementById('otpVerificationForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const verifyBtn = document.getElementById('verifyBtn');
        const otpCode = document.getElementById('otp_code').value;

        // Validate OTP
        if (!/^[0-9]{6}$/.test(otpCode)) {
            showError('otp_code', 'Please enter a valid 6-digit OTP');
            return;
        }

        // Clear previous errors
        clearErrors();

        // Show loading state
        verifyBtn.disabled = true;
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifying...';

        fetch('<?php echo e(route("mobile.verify-otp")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showSuccess('Login successful! Redirecting...');
                
                // Redirect after short delay
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            } else {
                showError('otp_code', data.message);
                
                // Show name field for new users if needed
                if (data.message.includes('new user') || data.message.includes('name')) {
                    document.getElementById('nameField').classList.remove('d-none');
                }
                
                // Show attempts left if available
                if (data.attempts_left) {
                    showError('otp_code', `${data.message} (${data.attempts_left} attempts left)`);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('otp_code', 'Verification failed. Please try again.');
        })
        .finally(() => {
            // Reset button state
            verifyBtn.disabled = false;
            verifyBtn.innerHTML = '<i class="fas fa-check me-2"></i>Verify & Continue';
        });
    });

    function resendOtp() {
        const resendBtn = document.getElementById('resendBtn');
        
        if (resendBtn.disabled) return;
        
        resendBtn.disabled = true;
        resendBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

        fetch('<?php echo e(route("mobile.resend-otp")); ?>', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('OTP sent successfully!');
                startResendTimer();
            } else {
                showError('otp_code', data.message);
                resendBtn.disabled = false;
                resendBtn.textContent = 'Resend OTP';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('otp_code', 'Failed to resend OTP. Please try again.');
            resendBtn.disabled = false;
            resendBtn.textContent = 'Resend OTP';
        });
    }

    function showError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId.replace('_', '') + 'Error');
        
        field.classList.add('is-invalid');
        if (errorDiv) {
            errorDiv.textContent = message;
        }
    }

    function showSuccess(message) {
        // Create or update success message
        let successDiv = document.getElementById('successMessage');
        if (!successDiv) {
            successDiv = document.createElement('div');
            successDiv.id = 'successMessage';
            successDiv.className = 'alert alert-success mt-3';
            document.querySelector('.card-body').appendChild(successDiv);
        }
        successDiv.textContent = message;
    }

    function clearErrors() {
        const fields = ['otp_code', 'name'];
        fields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId.replace('_', '') + 'Error');
            
            if (field) field.classList.remove('is-invalid');
            if (errorDiv) errorDiv.textContent = '';
        });
        
        // Remove success message
        const successDiv = document.getElementById('successMessage');
        if (successDiv) {
            successDiv.remove();
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/auth/otp-verification.blade.php ENDPATH**/ ?>