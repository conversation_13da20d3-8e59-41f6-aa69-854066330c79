@extends('layouts.app')

@section('title', 'My Profile - ShreeJi Jewelry')
@section('description', 'Manage your ShreeJi account profile, addresses, and preferences.')

@section('content')
<!-- Page Header -->
<section class="py-4 bg-light">
    <div class="container">
        <h1 class="font-playfair display-6 mb-2">My Profile</h1>
        <p class="text-muted mb-0">Manage your account information and preferences</p>
    </div>
</section>

<!-- Profile Content -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Profile Sidebar -->
            <div class="col-lg-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        @if($user->avatar)
                            <img src="{{ $user->avatar }}" alt="Profile Picture" class="rounded-circle mb-3" width="100" height="100">
                        @else
                            <div class="bg-primary-brown text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                                 style="width: 100px; height: 100px; font-size: 36px;">
                                {{ substr($user->name, 0, 1) }}
                            </div>
                        @endif
                        <h5 class="font-playfair mb-1">{{ $user->name }}</h5>
                        @if($user->email)
                            <p class="text-muted mb-2">{{ $user->email }}</p>
                        @endif
                        @if($user->phone)
                            <p class="text-muted mb-2">
                                <i class="fas fa-mobile-alt me-1"></i>{{ $user->formatted_phone }}
                            </p>
                        @endif
                        <span class="badge bg-primary-pink">{{ ucfirst($user->membership_level ?? 'Regular') }} Member</span>
                        
                        <div class="mt-4">
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2">Change Photo</button>
                            <button class="btn btn-outline-secondary btn-sm w-100">Remove Photo</button>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body">
                        <h6 class="font-playfair mb-3">Account Summary</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Orders:</span>
                            <strong>{{ $user->orders()->count() }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Spent:</span>
                            <strong class="text-primary-pink">₹{{ number_format($user->total_spent ?? 0, 0) }}</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Reward Points:</span>
                            <strong class="text-warning">{{ $user->reward_points ?? 0 }}</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Member Since:</span>
                            <strong>{{ $user->created_at->format('M Y') }}</strong>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Profile Forms -->
            <div class="col-lg-9">
                <!-- Personal Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Personal Information</h5>
                    </div>
                    <div class="card-body p-4">
                        <form id="personalInfoForm">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="{{ $user->name }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="{{ $user->email }}"
                                           placeholder="Enter your email address"
                                           {{ $user->isMobileOnly() ? '' : 'required' }}>
                                    @if($user->isMobileOnly())
                                        <small class="text-muted">Optional - Add email to enable email login</small>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="{{ $user->phone }}" required>
                                    @if($user->is_mobile_verified)
                                        <small class="text-success">
                                            <i class="fas fa-check-circle me-1"></i>Verified
                                        </small>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <label for="dateOfBirth" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="dateOfBirth" name="date_of_birth"
                                           value="{{ $user->date_of_birth ? $user->date_of_birth->format('Y-m-d') : '' }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" {{ $user->gender === 'male' ? 'selected' : '' }}>Male</option>
                                        <option value="female" {{ $user->gender === 'female' ? 'selected' : '' }}>Female</option>
                                        <option value="other" {{ $user->gender === 'other' ? 'selected' : '' }}>Other</option>
                                        <option value="prefer-not-to-say" {{ $user->gender === 'prefer-not-to-say' ? 'selected' : '' }}>Prefer not to say</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary-pink">Update Information</button>
                                <button type="button" class="btn btn-outline-secondary ms-2">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Account Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Account Information</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Member Since</label>
                                <p class="mb-0 fw-semibold">{{ $user->created_at->format('F Y') }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Membership Level</label>
                                <p class="mb-0">
                                    <span class="badge" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                                        {{ ucfirst($user->membership_level ?? 'Regular') }}
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Total Orders</label>
                                <p class="mb-0 fw-semibold">{{ $user->orders()->count() }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Total Spent</label>
                                <p class="mb-0 fw-semibold">₹{{ number_format($user->total_spent ?? 0) }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Reward Points</label>
                                <p class="mb-0 fw-semibold">{{ $user->reward_points ?? 0 }} points</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Account Status</label>
                                <p class="mb-0">
                                    <span class="badge bg-success">{{ ucfirst($user->status ?? 'Active') }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Preferences -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Preferences</h5>
                    </div>
                    <div class="card-body p-4">
                        <form id="preferencesForm" action="{{ route('profile.preferences') }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="language" class="form-label">Language</label>
                                    <select class="form-select" id="language" name="language">
                                        @php
                                            $userPrefs = $user->preferences ?? [];
                                            $currentLang = $userPrefs['language'] ?? 'en';
                                        @endphp
                                        <option value="en" {{ $currentLang == 'en' ? 'selected' : '' }}>English</option>
                                        <option value="hi" {{ $currentLang == 'hi' ? 'selected' : '' }}>Hindi</option>
                                        <option value="mr" {{ $currentLang == 'mr' ? 'selected' : '' }}>Marathi</option>
                                        <option value="gu" {{ $currentLang == 'gu' ? 'selected' : '' }}>Gujarati</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="currency" class="form-label">Currency</label>
                                    <select class="form-select" id="currency" name="currency">
                                        @php
                                            $currentCurrency = $userPrefs['currency'] ?? 'inr';
                                        @endphp
                                        <option value="inr" {{ $currentCurrency == 'inr' ? 'selected' : '' }}>Indian Rupee (₹)</option>
                                        <option value="usd" {{ $currentCurrency == 'usd' ? 'selected' : '' }}>US Dollar ($)</option>
                                        <option value="eur" {{ $currentCurrency == 'eur' ? 'selected' : '' }}>Euro (€)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <h6>Communication Preferences</h6>
                                @php
                                    $notifications = $userPrefs['notifications'] ?? [];
                                @endphp
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="emailNewsletter" name="email_newsletter" value="1" {{ ($notifications['email_newsletter'] ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="emailNewsletter">
                                        Email newsletters and promotions
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="smsNotifications" name="sms_notifications" value="1" {{ ($notifications['sms_notifications'] ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="smsNotifications">
                                        SMS notifications for orders
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="birthdayOffers" name="birthday_offers" value="1" {{ ($notifications['birthday_offers'] ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="birthdayOffers">
                                        Birthday special offers
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="newArrivals" name="new_arrivals" value="1" {{ ($notifications['new_arrivals'] ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="newArrivals">
                                        New arrival notifications
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary-pink">Save Preferences</button>
                                <button type="button" class="btn btn-outline-secondary ms-2">Reset</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Security -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Security</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6>Password</h6>
                                <p class="text-muted mb-2">Last changed 3 months ago</p>
                                <button class="btn btn-outline-primary">Change Password</button>
                            </div>
                            <div class="col-md-6">
                                <h6>Two-Factor Authentication</h6>
                                <p class="text-muted mb-2">Add an extra layer of security</p>
                                <button class="btn btn-outline-success">Enable 2FA</button>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6>Login Sessions</h6>
                                <p class="text-muted mb-2">Manage your active sessions</p>
                                <button class="btn btn-outline-secondary">View Sessions</button>
                            </div>
                            <div class="col-md-6">
                                <h6>Account Deletion</h6>
                                <p class="text-muted mb-2">Permanently delete your account</p>
                                <button class="btn btn-outline-danger">Delete Account</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


@endsection

@push('scripts')
<script>
    document.getElementById('personalInfoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Personal information updated successfully!');
    });
    
    // Preferences form is now handled by normal form submission
    // The form will submit to the backend route
</script>
@endpush
