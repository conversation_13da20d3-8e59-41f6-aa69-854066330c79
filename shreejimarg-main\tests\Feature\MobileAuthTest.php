<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\OtpVerification;
use App\Services\TwilioService;
use Mockery;

class MobileAuthTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Twilio service to avoid actual SMS sending during tests
        $this->mock(TwilioService::class, function ($mock) {
            $mock->shouldReceive('sendOtp')->andReturn([
                'success' => true,
                'message_sid' => 'test_message_sid',
                'message' => 'OTP sent successfully'
            ]);
            
            $mock->shouldReceive('generateAndSendOtp')->andReturn([
                'success' => true,
                'message' => 'OTP sent successfully to your mobile number',
                'otp_id' => 1,
                'expires_at' => now()->addMinutes(5),
                'message_sid' => 'test_message_sid'
            ]);
            
            $mock->shouldReceive('sendOrderConfirmation')->andReturn([
                'success' => true,
                'message_sid' => 'test_order_confirmation_sid',
                'message' => 'Order confirmation sent successfully'
            ]);
        });
    }

    /** @test */
    public function user_can_access_mobile_login_page()
    {
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        $response->assertViewIs('auth.mobile-login');
    }

    /** @test */
    public function user_can_send_otp_with_valid_phone_number()
    {
        $phone = '**********';
        
        $response = $this->post('/mobile/send-otp', [
            'phone' => $phone,
            'purpose' => 'login'
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'phone' => $phone
        ]);
        
        // Check OTP was created in database
        $this->assertDatabaseHas('otp_verifications', [
            'phone' => $phone,
            'purpose' => 'login',
            'is_verified' => false
        ]);
    }

    /** @test */
    public function user_cannot_send_otp_with_invalid_phone_number()
    {
        $response = $this->post('/mobile/send-otp', [
            'phone' => '123', // Invalid phone
            'purpose' => 'login'
        ]);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['phone']);
    }

    /** @test */
    public function user_can_verify_otp_and_login()
    {
        $phone = '**********';
        $otpCode = '123456';
        
        // Create OTP record
        OtpVerification::create([
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => 'login',
            'expires_at' => now()->addMinutes(5),
            'session_id' => session()->getId(),
            'ip_address' => '127.0.0.1'
        ]);
        
        // Set session data
        session(['otp_phone' => $phone, 'otp_purpose' => 'login']);
        
        $response = $this->post('/mobile/verify-otp', [
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => 'login',
            'name' => 'Test User'
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Login successful!'
        ]);
        
        // Check user was created and logged in
        $this->assertDatabaseHas('users', [
            'phone' => $phone,
            'name' => 'Test User',
            'is_mobile_verified' => true
        ]);
        
        $this->assertAuthenticated();
    }

    /** @test */
    public function user_cannot_verify_with_wrong_otp()
    {
        $phone = '**********';
        $correctOtp = '123456';
        $wrongOtp = '654321';
        
        // Create OTP record
        OtpVerification::create([
            'phone' => $phone,
            'otp_code' => $correctOtp,
            'purpose' => 'login',
            'expires_at' => now()->addMinutes(5),
            'session_id' => session()->getId(),
            'ip_address' => '127.0.0.1'
        ]);
        
        session(['otp_phone' => $phone, 'otp_purpose' => 'login']);
        
        $response = $this->post('/mobile/verify-otp', [
            'phone' => $phone,
            'otp_code' => $wrongOtp,
            'purpose' => 'login'
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Invalid OTP code.'
        ]);
        
        $this->assertGuest();
    }

    /** @test */
    public function user_cannot_verify_expired_otp()
    {
        $phone = '**********';
        $otpCode = '123456';
        
        // Create expired OTP record
        OtpVerification::create([
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => 'login',
            'expires_at' => now()->subMinutes(1), // Expired
            'session_id' => session()->getId(),
            'ip_address' => '127.0.0.1'
        ]);
        
        session(['otp_phone' => $phone, 'otp_purpose' => 'login']);
        
        $response = $this->post('/mobile/verify-otp', [
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => 'login'
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'Invalid or expired OTP.'
        ]);
        
        $this->assertGuest();
    }

    /** @test */
    public function existing_user_can_login_with_mobile()
    {
        $phone = '**********';
        $user = User::factory()->create([
            'phone' => $phone,
            'is_mobile_verified' => true
        ]);
        
        $otpCode = '123456';
        
        // Create OTP record
        OtpVerification::create([
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => 'login',
            'expires_at' => now()->addMinutes(5),
            'session_id' => session()->getId(),
            'ip_address' => '127.0.0.1'
        ]);
        
        session(['otp_phone' => $phone, 'otp_purpose' => 'login']);
        
        $response = $this->post('/mobile/verify-otp', [
            'phone' => $phone,
            'otp_code' => $otpCode,
            'purpose' => 'login'
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Login successful!'
        ]);
        
        $this->assertAuthenticatedAs($user);
    }

    /** @test */
    public function guest_can_access_checkout_and_be_prompted_for_mobile()
    {
        // Add item to cart first (you'll need to implement this based on your cart system)
        // For now, we'll just test the checkout redirect
        
        $response = $this->get('/checkout');
        
        // Should redirect to guest checkout for unauthenticated users
        $response->assertStatus(200);
        $response->assertViewIs('checkout.guest');
    }

    /** @test */
    public function mobile_only_user_can_set_password()
    {
        $user = User::factory()->create([
            'phone' => '**********',
            'email' => null,
            'password' => null,
            'is_mobile_verified' => true
        ]);
        
        // Create an order for the user to make them eligible
        $user->orders()->create([
            'order_number' => 'ORD-' . time(),
            'total_amount' => 1000,
            'status' => 'completed'
        ]);
        
        $this->actingAs($user);
        
        $response = $this->post('/account/set-password', [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]);
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Password set successfully! You can now login with email and password.'
        ]);
        
        // Check user was updated
        $user->refresh();
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertNotNull($user->password);
        $this->assertFalse($user->isMobileOnly());
    }

    /** @test */
    public function otp_cleanup_removes_expired_records()
    {
        // Create expired OTP
        OtpVerification::create([
            'phone' => '**********',
            'otp_code' => '123456',
            'purpose' => 'login',
            'expires_at' => now()->subHour(),
            'session_id' => 'test_session',
            'ip_address' => '127.0.0.1'
        ]);
        
        // Create valid OTP
        OtpVerification::create([
            'phone' => '9876543211',
            'otp_code' => '654321',
            'purpose' => 'login',
            'expires_at' => now()->addMinutes(5),
            'session_id' => 'test_session_2',
            'ip_address' => '127.0.0.1'
        ]);
        
        $this->assertEquals(2, OtpVerification::count());
        
        // Run cleanup
        OtpVerification::cleanup();
        
        // Only valid OTP should remain
        $this->assertEquals(1, OtpVerification::count());
        $this->assertDatabaseHas('otp_verifications', [
            'phone' => '9876543211'
        ]);
        $this->assertDatabaseMissing('otp_verifications', [
            'phone' => '**********'
        ]);
    }
}
