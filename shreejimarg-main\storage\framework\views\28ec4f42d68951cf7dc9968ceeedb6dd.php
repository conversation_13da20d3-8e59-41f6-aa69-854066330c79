<?php $__env->startSection('title', 'My Dashboard - ShreeJi Jewelry'); ?>
<?php $__env->startSection('description', 'Manage your ShreeJi account, view orders, track shipments, and access exclusive member benefits.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Dashboard Header -->
<section class="py-4 bg-gradient-pink text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="font-playfair display-6 mb-2">Welcome back, <?php echo e($user->name); ?>!</h1>
                <p class="mb-0">Manage your account and explore exclusive member benefits</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex align-items-center justify-content-md-end gap-3">
                    <div class="text-center">
                        <div class="h4 mb-0">₹<?php echo e(number_format($totalSpent, 0)); ?></div>
                        <small>Total Spent</small>
                    </div>
                    <div class="text-center">
                        <div class="h4 mb-0"><?php echo e(ucfirst($membershipLevel)); ?></div>
                        <small>Member Status</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <!-- Quick Actions -->
            <div class="col-lg-8">
                <div class="row g-4 mb-4">
                    <div class="col-md-3 col-6">
                        <a href="<?php echo e(route('orders')); ?>" class="text-decoration-none">
                            <div class="card border-0 shadow-sm h-100 text-center hover-card">
                                <div class="card-body p-3">
                                    <i class="fas fa-shopping-bag text-primary-brown fs-2 mb-2"></i>
                                    <h6 class="mb-1">My Orders</h6>
                                    <small class="text-muted"><?php echo e($totalOrders); ?> Orders</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="<?php echo e(route('wishlist')); ?>" class="text-decoration-none">
                            <div class="card border-0 shadow-sm h-100 text-center hover-card">
                                <div class="card-body p-3">
                                    <i class="fas fa-heart text-primary-brown fs-2 mb-2"></i>
                                    <h6 class="mb-1">Wishlist</h6>
                                    <small class="text-muted"><?php echo e($wishlistItems->count()); ?> Items</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="<?php echo e(route('profile')); ?>" class="text-decoration-none">
                            <div class="card border-0 shadow-sm h-100 text-center hover-card">
                                <div class="card-body p-3">
                                    <i class="fas fa-user-edit text-primary-brown fs-2 mb-2"></i>
                                    <h6 class="mb-1">Profile</h6>
                                    <small class="text-muted">Edit Details</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="<?php echo e(route('contact')); ?>" class="text-decoration-none">
                            <div class="card border-0 shadow-sm h-100 text-center hover-card">
                                <div class="card-body p-3">
                                    <i class="fas fa-headset text-primary-brown fs-2 mb-2"></i>
                                    <h6 class="mb-1">Support</h6>
                                    <small class="text-muted">Get Help</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- Recent Orders -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Orders</h5>
                        <a href="<?php echo e(route('orders')); ?>" class="text-primary-brown text-decoration-none">View All</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Date</th>
                                        <th>Items</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><strong><?php echo e($order->order_number); ?></strong></td>
                                        <td><?php echo e($order->created_at->format('M d, Y')); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($order->orderItems->first() && $order->orderItems->first()->product && $order->orderItems->first()->product->images): ?>
                                                    <img src="<?php echo e($order->orderItems->first()->product->images[0]); ?>"
                                                         alt="Product" class="rounded me-2" width="30" height="30">
                                                <?php else: ?>
                                                    <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center"
                                                         style="width: 30px; height: 30px;">
                                                        <i class="fas fa-gem text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <span>
                                                    <?php if($order->orderItems->first()): ?>
                                                        <?php echo e($order->orderItems->first()->product_name); ?>

                                                        <?php if($order->orderItems->count() > 1): ?>
                                                            + <?php echo e($order->orderItems->count() - 1); ?> more
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        Order Items
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td><strong>₹<?php echo e(number_format($order->total_amount, 0)); ?></strong></td>
                                        <td>
                                            <span class="badge <?php echo e($order->getStatusBadgeClass()); ?>">
                                                <?php echo e($order->getStatusDisplayName()); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('order.detail', $order->id)); ?>" class="btn btn-outline-primary btn-sm">
                                                <?php if(in_array($order->status, ['shipped', 'out_for_delivery'])): ?>
                                                    Track
                                                <?php else: ?>
                                                    View
                                                <?php endif; ?>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="fas fa-shopping-bag fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">No orders yet</p>
                                            <a href="<?php echo e(route('products')); ?>" class="btn btn-primary-pink btn-sm mt-2">Start Shopping</a>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Wishlist Preview -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Your Wishlist</h5>
                        <a href="<?php echo e(route('wishlist')); ?>" class="text-primary-brown text-decoration-none">
                            View All (<?php echo e($wishlistItems->count()); ?>)
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if($wishlistItems->count() > 0): ?>
                        <div class="row g-3">
                            <?php $__currentLoopData = $wishlistItems->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wishlistItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <?php if($wishlistItem->product && $wishlistItem->product->images && count($wishlistItem->product->images) > 0): ?>
                                        <img src="<?php echo e($wishlistItem->product->images[0]); ?>"
                                             alt="<?php echo e($wishlistItem->product->name); ?>" class="rounded me-3" width="60" height="60">
                                    <?php else: ?>
                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                             style="width: 60px; height: 60px;">
                                            <i class="fas fa-gem text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex-fill">
                                        <h6 class="mb-1"><?php echo e($wishlistItem->product->name); ?></h6>
                                        <p class="text-muted mb-1"><?php echo e($wishlistItem->product->category->name ?? 'Jewelry'); ?></p>
                                        <strong class="text-primary-brown">₹<?php echo e(number_format($wishlistItem->product->current_price, 0)); ?></strong>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm"
                                            onclick="addToCart(<?php echo e($wishlistItem->product->id); ?>)">
                                        Add to Cart
                                    </button>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-heart fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Your wishlist is empty</p>
                            <a href="<?php echo e(route('products')); ?>" class="btn btn-primary-pink btn-sm mt-2">Browse Products</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Account Summary -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-gradient-pink text-white">
                        <h5 class="mb-0">Account Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <?php if($user->avatar): ?>
                                <img src="<?php echo e($user->avatar); ?>"
                                     alt="Profile" class="rounded-circle me-3" width="60" height="60">
                            <?php else: ?>
                                <div class="bg-primary-brown text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 60px; height: 60px; font-size: 24px;">
                                    <?php echo e(substr($user->name, 0, 1)); ?>

                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-0"><?php echo e($user->name); ?></h6>
                                <small class="text-muted"><?php echo e($user->email); ?></small>
                                <br><span class="badge bg-primary-brown"><?php echo e(ucfirst($membershipLevel)); ?> Member</span>
                            </div>
                        </div>

                        <div class="row g-3 text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <div class="h5 text-primary-brown mb-0"><?php echo e($totalOrders); ?></div>
                                    <small class="text-muted">Orders</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <div class="h5 text-primary-brown mb-0"><?php echo e($wishlistItems->count()); ?></div>
                                    <small class="text-muted">Wishlist</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="h5 text-primary-brown mb-0"><?php echo e($rewardPoints); ?></div>
                                <small class="text-muted">Points</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Membership Benefits -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><?php echo e(ucfirst($membershipLevel)); ?> Benefits</h6>
                    </div>
                    <div class="card-body">
                        <?php if($membershipLevel === 'vip'): ?>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-crown text-warning me-2"></i>
                            <small>15% off all purchases</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shipping-fast text-primary-brown me-2"></i>
                            <small>Free express shipping</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-gift text-success me-2"></i>
                            <small>Birthday special offers</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-headset text-info me-2"></i>
                            <small>Priority customer support</small>
                        </div>
                        <?php elseif($membershipLevel === 'premium'): ?>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-percentage text-warning me-2"></i>
                            <small>10% off all purchases</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shipping-fast text-primary-brown me-2"></i>
                            <small>Free standard shipping</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-gift text-success me-2"></i>
                            <small>Birthday special offers</small>
                        </div>
                        <?php else: ?>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-percentage text-warning me-2"></i>
                            <small>5% off on orders above ₹50,000</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-truck text-primary-brown me-2"></i>
                            <small>Free shipping on orders above ₹25,000</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-star text-success me-2"></i>
                            <small>Earn reward points</small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Special Offers -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Special Offers</h6>
                    </div>
                    <div class="card-body">
                        <div class="offer-item mb-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary-brown text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-percent"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">20% Off Earrings</h6>
                                    <small class="text-muted">Valid until Jan 31</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="offer-item">
                            <div class="d-flex align-items-center">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">Free Gift Wrapping</h6>
                                    <small class="text-muted">On orders above ₹50,000</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recommended Products -->
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="font-playfair mb-4">Recommended for You</h3>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Recommended Product">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                        <span class="badge bg-primary-brown position-absolute top-0 start-0 m-2">Recommended</span>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Bridal Jewelry Set</h5>
                        <p class="card-text text-muted">22K Gold with Pearls</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-brown fw-bold">₹125,000</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Recommended Product">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Diamond Necklace</h5>
                        <p class="card-text text-muted">18K White Gold</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-brown fw-bold">₹85,000</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1611591437281-460bfbe1220a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Recommended Product">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Tennis Bracelet</h5>
                        <p class="card-text text-muted">18K Rose Gold</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-brown fw-bold">₹42,000</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Recommended Product">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Eternity Ring</h5>
                        <p class="card-text text-muted">Platinum with Diamonds</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-brown fw-bold">₹95,000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.hover-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.hover-card:hover .text-muted {
    color: var(--primary-brown) !important;
}

.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function addToCart(productId, size = null) {
    fetch('<?php echo e(route("cart.add")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: 1,
            size: size
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Product added to cart successfully!', 'success');
            // Update cart count if there's a cart counter on the page
            updateCartCount();
        } else {
            showAlert(data.message || 'Failed to add product to cart', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred. Please try again.', 'danger');
    });
}

function updateCartCount() {
    fetch('<?php echo e(route("api.cart.count")); ?>')
        .then(response => response.json())
        .then(data => {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = data.count;
            });
        })
        .catch(error => {
            console.error('Error updating cart count:', error);
        });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-refresh dashboard data every 60 seconds
setInterval(() => {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 60000);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/user/dashboard.blade.php ENDPATH**/ ?>