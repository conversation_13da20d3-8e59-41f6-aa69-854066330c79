<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!Auth::user()->isAdmin()) {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    public function dashboard()
    {
        // Get statistics
        $totalProducts = Product::count();
        $activeProducts = Product::active()->count();
        $lowStockProducts = Product::where('stock_quantity', '<=', 5)->count();
        $outOfStockProducts = Product::where('in_stock', false)->count();

        $totalOrders = Order::count();
        $pendingOrders = Order::where('status', 'pending')->count();
        $todayOrders = Order::whereDate('created_at', today())->count();
        $todayRevenue = Order::whereDate('created_at', today())->sum('total_amount');

        $totalCustomers = User::where('role', 'customer')->count();
        $newCustomers = User::where('role', 'customer')->whereDate('created_at', today())->count();

        // Get recent orders
        $recentOrders = Order::with('user', 'orderItems.product')
            ->recent()
            ->limit(10)
            ->get();

        // Get top selling products
        $topProducts = Product::withCount(['orderItems' => function($query) {
            $query->whereHas('order', function($q) {
                $q->whereMonth('created_at', now()->month);
            });
        }])
        ->orderBy('order_items_count', 'desc')
        ->limit(5)
        ->get();

        return view('admin.dashboard', compact(
            'totalProducts', 'activeProducts', 'lowStockProducts', 'outOfStockProducts',
            'totalOrders', 'pendingOrders', 'todayOrders', 'todayRevenue',
            'totalCustomers', 'newCustomers', 'recentOrders', 'topProducts'
        ));
    }

    // Product Management
    public function products()
    {
        $products = Product::with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        // Get product statistics
        $totalProducts = Product::count();
        $activeProducts = Product::active()->count();
        $lowStockProducts = Product::where('stock_quantity', '<=', 5)->count();
        $outOfStockProducts = Product::where('in_stock', false)->count();

        return view('admin.products.index', compact(
            'products', 'totalProducts', 'activeProducts', 'lowStockProducts', 'outOfStockProducts'
        ));
    }

    public function createProduct()
    {
        $categories = Category::active()->ordered()->get();

        if ($categories->isEmpty()) {
            throw new \InvalidArgumentException('No active categories available. Please create and activate at least one category before adding products.');
        }

        return view('admin.products.create', compact('categories'));
    }

    public function storeProduct(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string|max:255',
                'sku' => 'required|string|unique:products,sku',
                'description' => 'required|string',
                'short_description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0|lt:price',
                'stock_quantity' => 'required|integer|min:0',
                'weight' => 'nullable|numeric|min:0',
                'metal_type' => 'nullable|string',
                'metal_purity' => 'nullable|string',
                'stone_type' => 'nullable|string',
                'stone_weight' => 'nullable|numeric|min:0',
                'certification' => 'nullable|string',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                'specifications' => 'nullable',
                'sizes' => 'nullable'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate category exists and is active
            $category = Category::find($request->category_id);
            if (!$category) {
                throw new \InvalidArgumentException('Selected category does not exist.');
            }
            if (!$category->is_active) {
                throw new \InvalidArgumentException('Cannot add products to inactive category. Please activate the category first.');
            }

            // Validate sale price logic
            if ($request->sale_price && $request->sale_price >= $request->price) {
                throw new \InvalidArgumentException('Sale price must be less than regular price.');
            }

            $data = $request->except(['images']);
            $data['slug'] = Str::slug($request->name);
            $data['status'] = 'active';
            $data['in_stock'] = $request->stock_quantity > 0;
            $data['is_featured'] = $request->has('is_featured');

            // Process sizes and specifications
            if ($request->has('sizes') && is_array($request->sizes)) {
                $data['sizes'] = array_values(array_filter($request->sizes));
            } elseif ($request->has('sizes') && is_string($request->sizes)) {
                $data['sizes'] = array_values(array_filter(explode(',', $request->sizes)));
            }

            if ($request->has('specifications') && is_array($request->specifications)) {
                $data['specifications'] = $request->specifications;
            } elseif ($request->has('specifications') && is_string($request->specifications)) {
                try {
                    $data['specifications'] = json_decode($request->specifications, true) ?: ['description' => $request->specifications];
                } catch (\Exception $e) {
                    $data['specifications'] = ['description' => $request->specifications];
                }
            }

            // Handle image uploads
            $images = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    if (!$image->isValid()) {
                        throw new \InvalidArgumentException('One or more uploaded images are invalid.');
                    }
                    $path = $image->store('products', 'public');
                    // Ensure forward slashes are not escaped
                    $images[] = str_replace('\\', '/', $path);
                }
            }
            $data['images'] = $images;

            Product::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully!',
                'redirect_url' => route('admin.products.index')
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Product creation failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while creating the product.'
            ], 500);
        }
    }

    public function editProduct($id)
    {
        try {
            $product = Product::findOrFail($id);
            $categories = Category::active()->ordered()->get();

            if ($categories->isEmpty()) {
                throw new \InvalidArgumentException('No active categories available. Please create and activate at least one category before editing products.');
            }

            return view('admin.products.edit', compact('product', 'categories'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw new \InvalidArgumentException('Product not found.');
        }
    }

    public function updateProduct(Request $request, $id)
    {
        try {
            $product = Product::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string|max:255',
                'sku' => 'required|string|unique:products,sku,' . $id,
                'description' => 'required|string',
                'short_description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0|lt:price',
                'stock_quantity' => 'required|integer|min:0',
                'weight' => 'nullable|numeric|min:0',
                'metal_type' => 'nullable|string',
                'metal_purity' => 'nullable|string',
                'stone_type' => 'nullable|string',
                'stone_weight' => 'nullable|numeric|min:0',
                'certification' => 'nullable|string',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                'specifications' => 'nullable',
                'sizes' => 'nullable'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate category exists and is active
            $category = Category::find($request->category_id);
            if (!$category) {
                throw new \InvalidArgumentException('Selected category does not exist.');
            }
            if (!$category->is_active) {
                throw new \InvalidArgumentException('Cannot assign products to inactive category. Please activate the category first.');
            }

            // Validate sale price logic
            if ($request->sale_price && $request->sale_price >= $request->price) {
                throw new \InvalidArgumentException('Sale price must be less than regular price.');
            }

            $data = $request->except(['images']);
            $data['slug'] = Str::slug($request->name);
            $data['in_stock'] = $request->stock_quantity > 0;
            $data['is_featured'] = $request->has('is_featured');

            // Process sizes and specifications
            if ($request->has('sizes') && is_array($request->sizes)) {
                $data['sizes'] = array_values(array_filter($request->sizes));
            } elseif ($request->has('sizes') && is_string($request->sizes)) {
                $data['sizes'] = array_values(array_filter(explode(',', $request->sizes)));
            }

            if ($request->has('specifications') && is_array($request->specifications)) {
                $data['specifications'] = $request->specifications;
            } elseif ($request->has('specifications') && is_string($request->specifications)) {
                try {
                    $data['specifications'] = json_decode($request->specifications, true) ?: ['description' => $request->specifications];
                } catch (\Exception $e) {
                    $data['specifications'] = ['description' => $request->specifications];
                }
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                // Validate uploaded images
                foreach ($request->file('images') as $image) {
                    if (!$image->isValid()) {
                        throw new \InvalidArgumentException('One or more uploaded images are invalid.');
                    }
                }

                // Delete old images
                if ($product->images) {
                    foreach ($product->images as $oldImage) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }

                $images = [];
                foreach ($request->file('images') as $image) {
                    $path = $image->store('products', 'public');
                    // Ensure forward slashes are not escaped
                    $images[] = str_replace('\\', '/', $path);
                }
                $data['images'] = $images;
            }

            $product->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully!'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw new \InvalidArgumentException('Product not found.');
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Product update failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while updating the product.'
            ], 500);
        }
    }

    public function deleteProduct($id)
    {
        try {
            $product = Product::findOrFail($id);

            // Optional: Check if product has any completed orders (for business logic)
            // Comment out this check if you want to allow deletion of products with order history
            /*
            $completedOrdersCount = $product->orderItems()
                ->whereHas('order', function($query) {
                    $query->whereIn('status', ['delivered', 'shipped']);
                })
                ->count();

            if ($completedOrdersCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete product that has been delivered or shipped. Product has order history.'
                ], 400);
            }
            */

            // Delete images from storage
            if ($product->images && is_array($product->images)) {
                foreach ($product->images as $image) {
                    if ($image && Storage::disk('public')->exists($image)) {
                        Storage::disk('public')->delete($image);
                    }
                }
            }

            // Delete the product (cascade will handle related records)
            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully!'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found.'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Product deletion failed: ' . $e->getMessage(), [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while deleting the product: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkDeleteProducts(Request $request)
    {
        try {
            $request->validate([
                'product_ids' => 'required|array|min:1',
                'product_ids.*' => 'exists:products,id'
            ]);

            $productIds = $request->product_ids;
            $deletedCount = 0;
            $errors = [];

            foreach ($productIds as $productId) {
                try {
                    $product = Product::findOrFail($productId);

                    // Delete images from storage
                    if ($product->images && is_array($product->images)) {
                        foreach ($product->images as $image) {
                            if ($image && Storage::disk('public')->exists($image)) {
                                Storage::disk('public')->delete($image);
                            }
                        }
                    }

                    // Delete the product (cascade will handle related records)
                    $product->delete();
                    $deletedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Failed to delete product ID {$productId}: " . $e->getMessage();
                    Log::error('Bulk product deletion failed for product: ' . $productId, [
                        'product_id' => $productId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($deletedCount > 0 && empty($errors)) {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully deleted {$deletedCount} product(s)."
                ]);
            } elseif ($deletedCount > 0 && !empty($errors)) {
                return response()->json([
                    'success' => true,
                    'message' => "Deleted {$deletedCount} product(s). Some products could not be deleted.",
                    'errors' => $errors
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No products were deleted.',
                    'errors' => $errors
                ], 400);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Bulk product deletion failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred during bulk deletion.'
            ], 500);
        }
    }

    // Category Management
    public function categories()
    {
        $categories = Category::withCount('products')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return view('admin.categories.index', compact('categories'));
    }

    public function createCategory()
    {
        return view('admin.categories.create');
    }

    public function storeCategory(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:categories,name',
                'description' => 'nullable|string',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean',
                'is_featured' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $request->except(['image']);
            $data['slug'] = Str::slug($request->name);
            $data['is_active'] = $request->has('is_active');
            $data['is_featured'] = $request->has('is_featured');
            $data['sort_order'] = $request->sort_order ?? 0;

            // Handle image upload
            if ($request->hasFile('image')) {
                $path = $request->file('image')->store('categories', 'public');
                $data['image'] = $path;
            }

            Category::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully!',
                'redirect_url' => route('admin.categories.index')
            ]);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Failed to create category: ' . $e->getMessage());
        }
    }

    public function editCategory($id)
    {
        try {
            $category = Category::findOrFail($id);
            return view('admin.categories.edit', compact('category'));
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Category not found: ' . $e->getMessage());
        }
    }

    public function updateCategory(Request $request, $id)
    {
        try {
            $category = Category::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:categories,name,' . $id,
                'description' => 'nullable|string',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean',
                'is_featured' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $request->except(['image']);
            $data['slug'] = Str::slug($request->name);
            $data['is_active'] = $request->has('is_active');
            $data['is_featured'] = $request->has('is_featured');
            $data['sort_order'] = $request->sort_order ?? 0;

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($category->image) {
                    Storage::disk('public')->delete($category->image);
                }

                $path = $request->file('image')->store('categories', 'public');
                $data['image'] = $path;
            }

            $category->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Category updated successfully!'
            ]);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Failed to update category: ' . $e->getMessage());
        }
    }

    public function deleteCategory($id)
    {
        try {
            $category = Category::findOrFail($id);

            // Check if category has products
            if ($category->products()->count() > 0) {
                throw new \InvalidArgumentException('Cannot delete category that has products. Please move or delete the products first.');
            }

            // Delete image
            if ($category->image) {
                Storage::disk('public')->delete($category->image);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'Category deleted successfully!'
            ]);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Failed to delete category: ' . $e->getMessage());
        }
    }
}
