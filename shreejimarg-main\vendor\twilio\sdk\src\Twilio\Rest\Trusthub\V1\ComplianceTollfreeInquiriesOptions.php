<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Trusthub\V1;

use Twilio\Options;
use Twilio\Values;

abstract class ComplianceTollfreeInquiriesOptions
{
    /**
     * @param string $businessName The name of the business or organization using the Tollfree number.
     * @param string $businessWebsite The website of the business or organization using the Tollfree number.
     * @param string[] $useCaseCategories The category of the use case for the Tollfree Number. List as many are applicable..
     * @param string $useCaseSummary Use this to further explain how messaging is used by the business or organization.
     * @param string $productionMessageSample An example of message content, i.e. a sample message.
     * @param string[] $optInImageUrls Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
     * @param string $optInType
     * @param string $messageVolume Estimate monthly volume of messages from the Tollfree Number.
     * @param string $businessStreetAddress The address of the business or organization using the Tollfree number.
     * @param string $businessStreetAddress2 The address of the business or organization using the Tollfree number.
     * @param string $businessCity The city of the business or organization using the Tollfree number.
     * @param string $businessStateProvinceRegion The state/province/region of the business or organization using the Tollfree number.
     * @param string $businessPostalCode The postal code of the business or organization using the Tollfree number.
     * @param string $businessCountry The country of the business or organization using the Tollfree number.
     * @param string $additionalInformation Additional information to be provided for verification.
     * @param string $businessContactFirstName The first name of the contact for the business or organization using the Tollfree number.
     * @param string $businessContactLastName The last name of the contact for the business or organization using the Tollfree number.
     * @param string $businessContactEmail The email address of the contact for the business or organization using the Tollfree number.
     * @param string $businessContactPhone The phone number of the contact for the business or organization using the Tollfree number.
     * @param string $themeSetId Theme id for styling the inquiry form.
     * @param bool $skipMessagingUseCase Skip the messaging use case screen of the inquiry form.
     * @return CreateComplianceTollfreeInquiriesOptions Options builder
     */
    public static function create(
        
        string $businessName = Values::NONE,
        string $businessWebsite = Values::NONE,
        array $useCaseCategories = Values::ARRAY_NONE,
        string $useCaseSummary = Values::NONE,
        string $productionMessageSample = Values::NONE,
        array $optInImageUrls = Values::ARRAY_NONE,
        string $optInType = Values::NONE,
        string $messageVolume = Values::NONE,
        string $businessStreetAddress = Values::NONE,
        string $businessStreetAddress2 = Values::NONE,
        string $businessCity = Values::NONE,
        string $businessStateProvinceRegion = Values::NONE,
        string $businessPostalCode = Values::NONE,
        string $businessCountry = Values::NONE,
        string $additionalInformation = Values::NONE,
        string $businessContactFirstName = Values::NONE,
        string $businessContactLastName = Values::NONE,
        string $businessContactEmail = Values::NONE,
        string $businessContactPhone = Values::NONE,
        string $themeSetId = Values::NONE,
        bool $skipMessagingUseCase = Values::BOOL_NONE

    ): CreateComplianceTollfreeInquiriesOptions
    {
        return new CreateComplianceTollfreeInquiriesOptions(
            $businessName,
            $businessWebsite,
            $useCaseCategories,
            $useCaseSummary,
            $productionMessageSample,
            $optInImageUrls,
            $optInType,
            $messageVolume,
            $businessStreetAddress,
            $businessStreetAddress2,
            $businessCity,
            $businessStateProvinceRegion,
            $businessPostalCode,
            $businessCountry,
            $additionalInformation,
            $businessContactFirstName,
            $businessContactLastName,
            $businessContactEmail,
            $businessContactPhone,
            $themeSetId,
            $skipMessagingUseCase
        );
    }

}

class CreateComplianceTollfreeInquiriesOptions extends Options
    {
    /**
     * @param string $businessName The name of the business or organization using the Tollfree number.
     * @param string $businessWebsite The website of the business or organization using the Tollfree number.
     * @param string[] $useCaseCategories The category of the use case for the Tollfree Number. List as many are applicable..
     * @param string $useCaseSummary Use this to further explain how messaging is used by the business or organization.
     * @param string $productionMessageSample An example of message content, i.e. a sample message.
     * @param string[] $optInImageUrls Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
     * @param string $optInType
     * @param string $messageVolume Estimate monthly volume of messages from the Tollfree Number.
     * @param string $businessStreetAddress The address of the business or organization using the Tollfree number.
     * @param string $businessStreetAddress2 The address of the business or organization using the Tollfree number.
     * @param string $businessCity The city of the business or organization using the Tollfree number.
     * @param string $businessStateProvinceRegion The state/province/region of the business or organization using the Tollfree number.
     * @param string $businessPostalCode The postal code of the business or organization using the Tollfree number.
     * @param string $businessCountry The country of the business or organization using the Tollfree number.
     * @param string $additionalInformation Additional information to be provided for verification.
     * @param string $businessContactFirstName The first name of the contact for the business or organization using the Tollfree number.
     * @param string $businessContactLastName The last name of the contact for the business or organization using the Tollfree number.
     * @param string $businessContactEmail The email address of the contact for the business or organization using the Tollfree number.
     * @param string $businessContactPhone The phone number of the contact for the business or organization using the Tollfree number.
     * @param string $themeSetId Theme id for styling the inquiry form.
     * @param bool $skipMessagingUseCase Skip the messaging use case screen of the inquiry form.
     */
    public function __construct(
        
        string $businessName = Values::NONE,
        string $businessWebsite = Values::NONE,
        array $useCaseCategories = Values::ARRAY_NONE,
        string $useCaseSummary = Values::NONE,
        string $productionMessageSample = Values::NONE,
        array $optInImageUrls = Values::ARRAY_NONE,
        string $optInType = Values::NONE,
        string $messageVolume = Values::NONE,
        string $businessStreetAddress = Values::NONE,
        string $businessStreetAddress2 = Values::NONE,
        string $businessCity = Values::NONE,
        string $businessStateProvinceRegion = Values::NONE,
        string $businessPostalCode = Values::NONE,
        string $businessCountry = Values::NONE,
        string $additionalInformation = Values::NONE,
        string $businessContactFirstName = Values::NONE,
        string $businessContactLastName = Values::NONE,
        string $businessContactEmail = Values::NONE,
        string $businessContactPhone = Values::NONE,
        string $themeSetId = Values::NONE,
        bool $skipMessagingUseCase = Values::BOOL_NONE

    ) {
        $this->options['businessName'] = $businessName;
        $this->options['businessWebsite'] = $businessWebsite;
        $this->options['useCaseCategories'] = $useCaseCategories;
        $this->options['useCaseSummary'] = $useCaseSummary;
        $this->options['productionMessageSample'] = $productionMessageSample;
        $this->options['optInImageUrls'] = $optInImageUrls;
        $this->options['optInType'] = $optInType;
        $this->options['messageVolume'] = $messageVolume;
        $this->options['businessStreetAddress'] = $businessStreetAddress;
        $this->options['businessStreetAddress2'] = $businessStreetAddress2;
        $this->options['businessCity'] = $businessCity;
        $this->options['businessStateProvinceRegion'] = $businessStateProvinceRegion;
        $this->options['businessPostalCode'] = $businessPostalCode;
        $this->options['businessCountry'] = $businessCountry;
        $this->options['additionalInformation'] = $additionalInformation;
        $this->options['businessContactFirstName'] = $businessContactFirstName;
        $this->options['businessContactLastName'] = $businessContactLastName;
        $this->options['businessContactEmail'] = $businessContactEmail;
        $this->options['businessContactPhone'] = $businessContactPhone;
        $this->options['themeSetId'] = $themeSetId;
        $this->options['skipMessagingUseCase'] = $skipMessagingUseCase;
    }

    /**
     * The name of the business or organization using the Tollfree number.
     *
     * @param string $businessName The name of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessName(string $businessName): self
    {
        $this->options['businessName'] = $businessName;
        return $this;
    }

    /**
     * The website of the business or organization using the Tollfree number.
     *
     * @param string $businessWebsite The website of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessWebsite(string $businessWebsite): self
    {
        $this->options['businessWebsite'] = $businessWebsite;
        return $this;
    }

    /**
     * The category of the use case for the Tollfree Number. List as many are applicable..
     *
     * @param string[] $useCaseCategories The category of the use case for the Tollfree Number. List as many are applicable..
     * @return $this Fluent Builder
     */
    public function setUseCaseCategories(array $useCaseCategories): self
    {
        $this->options['useCaseCategories'] = $useCaseCategories;
        return $this;
    }

    /**
     * Use this to further explain how messaging is used by the business or organization.
     *
     * @param string $useCaseSummary Use this to further explain how messaging is used by the business or organization.
     * @return $this Fluent Builder
     */
    public function setUseCaseSummary(string $useCaseSummary): self
    {
        $this->options['useCaseSummary'] = $useCaseSummary;
        return $this;
    }

    /**
     * An example of message content, i.e. a sample message.
     *
     * @param string $productionMessageSample An example of message content, i.e. a sample message.
     * @return $this Fluent Builder
     */
    public function setProductionMessageSample(string $productionMessageSample): self
    {
        $this->options['productionMessageSample'] = $productionMessageSample;
        return $this;
    }

    /**
     * Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
     *
     * @param string[] $optInImageUrls Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
     * @return $this Fluent Builder
     */
    public function setOptInImageUrls(array $optInImageUrls): self
    {
        $this->options['optInImageUrls'] = $optInImageUrls;
        return $this;
    }

    /**
     * @param string $optInType
     * @return $this Fluent Builder
     */
    public function setOptInType(string $optInType): self
    {
        $this->options['optInType'] = $optInType;
        return $this;
    }

    /**
     * Estimate monthly volume of messages from the Tollfree Number.
     *
     * @param string $messageVolume Estimate monthly volume of messages from the Tollfree Number.
     * @return $this Fluent Builder
     */
    public function setMessageVolume(string $messageVolume): self
    {
        $this->options['messageVolume'] = $messageVolume;
        return $this;
    }

    /**
     * The address of the business or organization using the Tollfree number.
     *
     * @param string $businessStreetAddress The address of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessStreetAddress(string $businessStreetAddress): self
    {
        $this->options['businessStreetAddress'] = $businessStreetAddress;
        return $this;
    }

    /**
     * The address of the business or organization using the Tollfree number.
     *
     * @param string $businessStreetAddress2 The address of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessStreetAddress2(string $businessStreetAddress2): self
    {
        $this->options['businessStreetAddress2'] = $businessStreetAddress2;
        return $this;
    }

    /**
     * The city of the business or organization using the Tollfree number.
     *
     * @param string $businessCity The city of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessCity(string $businessCity): self
    {
        $this->options['businessCity'] = $businessCity;
        return $this;
    }

    /**
     * The state/province/region of the business or organization using the Tollfree number.
     *
     * @param string $businessStateProvinceRegion The state/province/region of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessStateProvinceRegion(string $businessStateProvinceRegion): self
    {
        $this->options['businessStateProvinceRegion'] = $businessStateProvinceRegion;
        return $this;
    }

    /**
     * The postal code of the business or organization using the Tollfree number.
     *
     * @param string $businessPostalCode The postal code of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessPostalCode(string $businessPostalCode): self
    {
        $this->options['businessPostalCode'] = $businessPostalCode;
        return $this;
    }

    /**
     * The country of the business or organization using the Tollfree number.
     *
     * @param string $businessCountry The country of the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessCountry(string $businessCountry): self
    {
        $this->options['businessCountry'] = $businessCountry;
        return $this;
    }

    /**
     * Additional information to be provided for verification.
     *
     * @param string $additionalInformation Additional information to be provided for verification.
     * @return $this Fluent Builder
     */
    public function setAdditionalInformation(string $additionalInformation): self
    {
        $this->options['additionalInformation'] = $additionalInformation;
        return $this;
    }

    /**
     * The first name of the contact for the business or organization using the Tollfree number.
     *
     * @param string $businessContactFirstName The first name of the contact for the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessContactFirstName(string $businessContactFirstName): self
    {
        $this->options['businessContactFirstName'] = $businessContactFirstName;
        return $this;
    }

    /**
     * The last name of the contact for the business or organization using the Tollfree number.
     *
     * @param string $businessContactLastName The last name of the contact for the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessContactLastName(string $businessContactLastName): self
    {
        $this->options['businessContactLastName'] = $businessContactLastName;
        return $this;
    }

    /**
     * The email address of the contact for the business or organization using the Tollfree number.
     *
     * @param string $businessContactEmail The email address of the contact for the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessContactEmail(string $businessContactEmail): self
    {
        $this->options['businessContactEmail'] = $businessContactEmail;
        return $this;
    }

    /**
     * The phone number of the contact for the business or organization using the Tollfree number.
     *
     * @param string $businessContactPhone The phone number of the contact for the business or organization using the Tollfree number.
     * @return $this Fluent Builder
     */
    public function setBusinessContactPhone(string $businessContactPhone): self
    {
        $this->options['businessContactPhone'] = $businessContactPhone;
        return $this;
    }

    /**
     * Theme id for styling the inquiry form.
     *
     * @param string $themeSetId Theme id for styling the inquiry form.
     * @return $this Fluent Builder
     */
    public function setThemeSetId(string $themeSetId): self
    {
        $this->options['themeSetId'] = $themeSetId;
        return $this;
    }

    /**
     * Skip the messaging use case screen of the inquiry form.
     *
     * @param bool $skipMessagingUseCase Skip the messaging use case screen of the inquiry form.
     * @return $this Fluent Builder
     */
    public function setSkipMessagingUseCase(bool $skipMessagingUseCase): self
    {
        $this->options['skipMessagingUseCase'] = $skipMessagingUseCase;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Trusthub.V1.CreateComplianceTollfreeInquiriesOptions ' . $options . ']';
    }
}

