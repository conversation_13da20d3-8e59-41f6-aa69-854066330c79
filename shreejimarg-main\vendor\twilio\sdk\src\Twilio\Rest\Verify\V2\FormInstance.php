<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Verify\V2;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;


/**
 * @property string $formType
 * @property array|null $forms
 * @property array|null $formMeta
 * @property string|null $url
 */
class FormInstance extends InstanceResource
{
    /**
     * Initialize the FormInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $formType The Type of this Form. Currently only `form-push` is supported.
     */
    public function __construct(Version $version, array $payload, ?string $formType = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'formType' => Values::array_get($payload, 'form_type'),
            'forms' => Values::array_get($payload, 'forms'),
            'formMeta' => Values::array_get($payload, 'form_meta'),
            'url' => Values::array_get($payload, 'url'),
        ];

        $this->solution = ['formType' => $formType ?: $this->properties['formType'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return FormContext Context for this FormInstance
     */
    protected function proxy(): FormContext
    {
        if (!$this->context) {
            $this->context = new FormContext(
                $this->version,
                $this->solution['formType']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the FormInstance
     *
     * @return FormInstance Fetched FormInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FormInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Verify.V2.FormInstance ' . \implode(' ', $context) . ']';
    }
}

