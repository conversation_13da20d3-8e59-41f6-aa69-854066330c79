@extends('layouts.app')

@section('title', 'ShreeJi - Exquisite Jewelry Collection')
@section('description', 'Discover beautiful handcrafted jewelry at ShreeJi. Browse our collection of rings, necklaces, earrings, and bracelets.')

@section('content')
<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 order-2 order-lg-1">
                <div class="hero-content">
                    <h1 class="font-playfair display-4 fw-bold mb-4">
                        Discover <span style="color: var(--primary-brown);">Exquisite</span> Jewelry
                    </h1>
                    <p class="lead mb-4 text-muted">
                        Embrace elegance with our handcrafted collection of fine jewelry.
                        Each piece tells a story of timeless beauty and exceptional craftsmanship.
                    </p>
                    <div class="d-flex flex-column flex-sm-row gap-3 mb-4">
                        <a href="{{ url('/collections') }}" class="btn" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                            <i class="fas fa-gem me-2"></i>Shop Collection
                        </a>
                        <a href="{{ url('/about') }}" class="btn" style="border: 1px solid #8B4513; color: #8B4513;">
                            <i class="fas fa-play me-2"></i>Our Story
                        </a>
                    </div>

                    <!-- Stats -->
                    <div class="row mt-4 mt-lg-5 stats-section">
                        <div class="col-4">
                            <h3 class="font-playfair mb-0 stat-number" style="color: #8B4513;">500+</h3>
                            <small class="text-muted stat-label">Happy Customers</small>
                        </div>
                        <div class="col-4">
                            <h3 class="font-playfair mb-0 stat-number" style="color: #8B4513;">50+</h3>
                            <small class="text-muted stat-label">Unique Designs</small>
                        </div>
                        <div class="col-4">
                            <h3 class="font-playfair mb-0 stat-number" style="color: #8B4513;">5★</h3>
                            <small class="text-muted stat-label">Customer Rating</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 order-1 order-lg-2 mb-4 mb-lg-0">
                <div class="hero-image-container position-relative">
                    <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                         alt="Beautiful Jewelry Collection"
                         class="img-fluid rounded-4 shadow-lg hero-main-image">

                    <!-- Floating Cards - Hidden on mobile, shown on larger screens -->
                    <div class="floating-card floating-card-1 position-absolute top-0 start-0 translate-middle d-none d-md-block">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-shipping-fast fs-4 mb-2" style="color: #8B4513;"></i>
                                <small class="d-block">Free Shipping</small>
                            </div>
                        </div>
                    </div>

                    <div class="floating-card floating-card-2 position-absolute bottom-0 end-0 translate-middle d-none d-md-block">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center p-3">
                                <i class="fas fa-certificate fs-4 mb-2" style="color: #8B4513;"></i>
                                <small class="d-block">Certified</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Features - Shown only on mobile -->
                <div class="row d-md-none mt-3">
                    <div class="col-6">
                        <div class="text-center p-2">
                            <i class="fas fa-shipping-fast fs-5 mb-1" style="color: #8B4513;"></i>
                            <small class="d-block text-muted">Free Shipping</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2">
                            <i class="fas fa-certificate fs-5 mb-1" style="color: #8B4513;"></i>
                            <small class="d-block text-muted">Certified</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Collections -->
<section id="collections" class="py-5">
    <div class="container">
        <h2 class="section-title">Featured Collections</h2>

        <div class="row g-4">
            <!-- Rings Collection -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                             class="card-img-top" alt="Rings Collection">
                        <div class="product-overlay">
                            <a href="{{ url('/collections/rings') }}" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Elegant Rings</h5>
                        <p class="card-text text-muted">Discover our stunning ring collection</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="fw-bold" style="color: #8B4513;">From ₹15,000</span>
                        </div>
                        <!-- Mobile View Button -->
                        <div class="d-block d-lg-none mt-3">
                            <a href="{{ url('/collections/rings') }}" class="btn btn-sm w-100" style="background-color: #8B4513; color: #F5F5DC;">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Necklaces Collection -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                             class="card-img-top" alt="Necklaces Collection">
                        <div class="product-overlay">
                            <a href="{{ url('/collections/necklaces') }}" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Graceful Necklaces</h5>
                        <p class="card-text text-muted">Exquisite necklaces for every occasion</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="fw-bold" style="color: #8B4513;">From ₹25,000</span>
                        </div>
                        <!-- Mobile View Button -->
                        <div class="d-block d-lg-none mt-3">
                            <a href="{{ url('/collections/necklaces') }}" class="btn btn-sm w-100" style="background-color: #8B4513; color: #F5F5DC;">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Earrings Collection -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                             class="card-img-top" alt="Earrings Collection">
                        <div class="product-overlay">
                            <a href="{{ url('/collections/earrings') }}" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Stunning Earrings</h5>
                        <p class="card-text text-muted">Beautiful earrings to complement your style</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="fw-bold" style="color: #8B4513;">From ₹8,000</span>
                        </div>
                        <!-- Mobile View Button -->
                        <div class="d-block d-lg-none mt-3">
                            <a href="{{ url('/collections/earrings') }}" class="btn btn-sm w-100" style="background-color: #8B4513; color: #F5F5DC;">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bracelets Collection -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1611591437281-460bfbe1220a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                             class="card-img-top" alt="Bracelets Collection">
                        <div class="product-overlay">
                            <a href="{{ url('/collections/bracelets') }}" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Charming Bracelets</h5>
                        <p class="card-text text-muted">Delicate bracelets for elegant wrists</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="fw-bold" style="color: #8B4513;">From ₹12,000</span>
                        </div>
                        <!-- Mobile View Button -->
                        <div class="d-block d-lg-none mt-3">
                            <a href="{{ url('/collections/bracelets') }}" class="btn btn-sm w-100" style="background-color: #8B4513; color: #F5F5DC;">
                                <i class="fas fa-eye me-2"></i>View Collection
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5">
            <a href="{{ url('/collections') }}" class="btn btn-outline-pink btn-lg">
                <i class="fas fa-th me-2"></i>View All Products
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">What Our Customers Say</h2>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm testimonial-card">
                    <div class="card-body text-center p-4">
                        <div class="mb-3 stars-rating">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4 testimonial-text">"Absolutely stunning jewelry! The quality is exceptional and the designs are so unique. I've received countless compliments on my necklace."</p>
                        <div class="d-flex align-items-center justify-content-center customer-info">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3 customer-avatar" width="50" height="50">
                            <div class="text-start">
                                <h6 class="mb-0 customer-name">Priya Sharma</h6>
                                <small class="text-muted customer-location">Mumbai</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm testimonial-card">
                    <div class="card-body text-center p-4">
                        <div class="mb-3 stars-rating">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4 testimonial-text">"ShreeJi has the most beautiful collection I've ever seen. The craftsmanship is incredible and the customer service is outstanding."</p>
                        <div class="d-flex align-items-center justify-content-center customer-info">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3 customer-avatar" width="50" height="50">
                            <div class="text-start">
                                <h6 class="mb-0 customer-name">Anita Patel</h6>
                                <small class="text-muted customer-location">Delhi</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm testimonial-card">
                    <div class="card-body text-center p-4">
                        <div class="mb-3 stars-rating">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text mb-4 testimonial-text">"Perfect for my wedding! The team helped me choose the most beautiful set. Every piece is a work of art. Highly recommended!"</p>
                        <div class="d-flex align-items-center justify-content-center customer-info">
                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                                 alt="Customer" class="rounded-circle me-3 customer-avatar" width="50" height="50">
                            <div class="text-start">
                                <h6 class="mb-0 customer-name">Kavya Reddy</h6>
                                <small class="text-muted customer-location">Bangalore</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    /* Home Page Specific Mobile-First Styles */

    /* Hero Section Enhancements */
    .hero-section {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.08), rgba(160, 82, 45, 0.08));
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(139, 69, 19, 0.05) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
        pointer-events: none;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .hero-image-container {
        position: relative;
        z-index: 2;
    }

    /* Product Cards Mobile Enhancements */
    .product-card {
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    }

    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }

    .product-card .card-img-top {
        transition: all 0.4s ease;
        border-radius: 20px 20px 0 0;
    }

    .product-card:hover .card-img-top {
        transform: scale(1.05);
    }

    /* Mobile Product Card Improvements */
    @media (max-width: 991.98px) {
        .product-card {
            margin-bottom: 1rem;
        }

        .product-overlay {
            display: none !important;
        }

        .product-card .card-body {
            padding: 1.5rem 1rem;
        }

        .product-card .card-title {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .product-card .card-text {
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
    }

    /* Testimonials Section Enhancements */
    .testimonial-card {
        border-radius: 20px;
        transition: all 0.4s ease;
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
    }

    .testimonial-card:hover {
        background: linear-gradient(145deg, #f8f9fa, #ffffff);
        box-shadow: 0 15px 35px rgba(255, 20, 147, 0.1) !important;
    }

    .stars-rating {
        font-size: 1.1rem;
    }

    @media (max-width: 767.98px) {
        .stars-rating {
            font-size: 1rem;
        }

        .testimonial-card .card-body {
            padding: 1.5rem 1rem;
        }

        .customer-info {
            flex-direction: column;
            text-align: center;
        }

        .customer-info .customer-avatar {
            margin-right: 0 !important;
            margin-bottom: 0.5rem;
        }

        .customer-info div {
            text-align: center !important;
        }
    }

    /* Button Enhancements */
    .btn {
        border-radius: 50px;
        font-weight: 600;
        letter-spacing: 0.5px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    /* Mobile Button Improvements */
    @media (max-width: 575.98px) {
        .btn {
            width: 100%;
            margin-bottom: 0.5rem;
            padding: 14px 20px;
            font-size: 1rem;
        }

        .hero-section .d-flex.gap-3 {
            flex-direction: column;
            gap: 0.75rem !important;
        }
    }

    /* Section Spacing */
    @media (max-width: 767.98px) {
        section {
            padding: 3rem 0 !important;
        }

        .section-title {
            margin-bottom: 2rem !important;
        }
    }

    /* Performance Optimizations */
    .hero-main-image,
    .card-img-top,
    .customer-avatar {
        will-change: transform;
    }

    /* Smooth scrolling for the entire page */
    html {
        scroll-behavior: smooth;
    }

    /* Focus states for accessibility */
    .btn:focus,
    .nav-link:focus,
    .card:focus {
        outline: 2px solid #8B4513;
        outline-offset: 2px;
    }
</style>
@endpush
