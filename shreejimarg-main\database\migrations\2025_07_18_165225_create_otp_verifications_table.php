<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('otp_verifications', function (Blueprint $table) {
            $table->id();
            $table->string('phone', 15)->index(); // Mobile number
            $table->string('otp_code', 6); // 6-digit OTP
            $table->enum('purpose', ['login', 'registration', 'checkout', 'test'])->default('login'); // Purpose of OTP
            $table->timestamp('expires_at'); // OTP expiry time (5 minutes)
            $table->boolean('is_verified')->default(false); // Whether OTP was verified
            $table->timestamp('verified_at')->nullable(); // When OTP was verified
            $table->integer('attempts')->default(0); // Number of verification attempts
            $table->integer('resend_count')->default(0); // Number of times OTP was resent
            $table->timestamp('last_resent_at')->nullable(); // Last resend time
            $table->string('session_id')->nullable(); // Session ID for guest users
            $table->ipAddress('ip_address')->nullable(); // IP address for security
            $table->timestamps();

            // Indexes for performance
            $table->index(['phone', 'purpose', 'is_verified']);
            $table->index(['expires_at']);
            $table->index(['session_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('otp_verifications');
    }
};
