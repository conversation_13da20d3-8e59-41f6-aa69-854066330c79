<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twi<PERSON>\TwiML\Voice;

use <PERSON><PERSON><PERSON>\TwiML\TwiML;

class ConversationRelay extends TwiML {
    /**
     * ConversationRelay constructor.
     *
     * @param array $attributes Optional attributes
     */
    public function __construct($attributes = []) {
        parent::__construct('ConversationRelay', null, $attributes);
    }

    /**
     * Add Language child.
     *
     * @param array $attributes Optional attributes
     * @return Language Child element.
     */
    public function language($attributes = []): Language {
        return $this->nest(new Language($attributes));
    }

    /**
     * Add Parameter child.
     *
     * @param array $attributes Optional attributes
     * @return Parameter Child element.
     */
    public function parameter($attributes = []): Parameter {
        return $this->nest(new Parameter($attributes));
    }

    /**
     * Add Url attribute.
     *
     * @param string $url URL of the remote service where the session is connected
     *                    to
     */
    public function setUrl($url): self {
        return $this->setAttribute('url', $url);
    }

    /**
     * Add Language attribute.
     *
     * @param string $language Language to be used for both text-to-speech and
     *                         transcription
     */
    public function setLanguage($language): self {
        return $this->setAttribute('language', $language);
    }

    /**
     * Add TtsLanguage attribute.
     *
     * @param string $ttsLanguage Language to be used for text-to-speech
     */
    public function setTtsLanguage($ttsLanguage): self {
        return $this->setAttribute('ttsLanguage', $ttsLanguage);
    }

    /**
     * Add TranscriptionLanguage attribute.
     *
     * @param string $transcriptionLanguage Language to be used for transcription
     */
    public function setTranscriptionLanguage($transcriptionLanguage): self {
        return $this->setAttribute('transcriptionLanguage', $transcriptionLanguage);
    }

    /**
     * Add TtsProvider attribute.
     *
     * @param string $ttsProvider Provider to be used for text-to-speech
     */
    public function setTtsProvider($ttsProvider): self {
        return $this->setAttribute('ttsProvider', $ttsProvider);
    }

    /**
     * Add Voice attribute.
     *
     * @param string $voice Voice to be used for text-to-speech
     */
    public function setVoice($voice): self {
        return $this->setAttribute('voice', $voice);
    }

    /**
     * Add TranscriptionProvider attribute.
     *
     * @param string $transcriptionProvider Provider to be used for transcription
     */
    public function setTranscriptionProvider($transcriptionProvider): self {
        return $this->setAttribute('transcriptionProvider', $transcriptionProvider);
    }

    /**
     * Add SpeechModel attribute.
     *
     * @param string $speechModel Speech model to be used for transcription
     */
    public function setSpeechModel($speechModel): self {
        return $this->setAttribute('speechModel', $speechModel);
    }

    /**
     * Add ProfanityFilter attribute.
     *
     * @param bool $profanityFilter Whether profanities should be filtered out of
     *                              the speech transcription
     */
    public function setProfanityFilter($profanityFilter): self {
        return $this->setAttribute('profanityFilter', $profanityFilter);
    }

    /**
     * Add DtmfDetection attribute.
     *
     * @param bool $dtmfDetection Whether DTMF tones should be detected and
     *                            reported in speech transcription
     */
    public function setDtmfDetection($dtmfDetection): self {
        return $this->setAttribute('dtmfDetection', $dtmfDetection);
    }

    /**
     * Add WelcomeGreeting attribute.
     *
     * @param string $welcomeGreeting The sentence to be played automatically when
     *                                the session is connected
     */
    public function setWelcomeGreeting($welcomeGreeting): self {
        return $this->setAttribute('welcomeGreeting', $welcomeGreeting);
    }

    /**
     * Add PartialPrompts attribute.
     *
     * @param bool $partialPrompts Whether partial prompts should be reported to
     *                             WebSocket server before the caller finishes
     *                             speaking
     */
    public function setPartialPrompts($partialPrompts): self {
        return $this->setAttribute('partialPrompts', $partialPrompts);
    }

    /**
     * Add WelcomeGreetingInterruptible attribute.
     *
     * @param string $welcomeGreetingInterruptible "Whether and how the input from
     *                                             a caller, such as speaking or
     *                                             DTMF can interrupt the welcome
     *                                             greeting
     */
    public function setWelcomeGreetingInterruptible($welcomeGreetingInterruptible): self {
        return $this->setAttribute('welcomeGreetingInterruptible', $welcomeGreetingInterruptible);
    }

    /**
     * Add Interruptible attribute.
     *
     * @param string $interruptible Whether and how the input from a caller, such
     *                              as speaking or DTMF can interrupt the play of
     *                              text-to-speech
     */
    public function setInterruptible($interruptible): self {
        return $this->setAttribute('interruptible', $interruptible);
    }

    /**
     * Add Preemptible attribute.
     *
     * @param bool $preemptible Whether subsequent text-to-speech or play media can
     *                          interrupt the on-going play of text-to-speech or
     *                          media
     */
    public function setPreemptible($preemptible): self {
        return $this->setAttribute('preemptible', $preemptible);
    }

    /**
     * Add Hints attribute.
     *
     * @param string $hints Phrases to help better accuracy in speech recognition
     *                      of these pharases
     */
    public function setHints($hints): self {
        return $this->setAttribute('hints', $hints);
    }

    /**
     * Add IntelligenceService attribute.
     *
     * @param string $intelligenceService The Conversational Intelligence Service
     *                                    id or unique name to be used for the
     *                                    session
     */
    public function setIntelligenceService($intelligenceService): self {
        return $this->setAttribute('intelligenceService', $intelligenceService);
    }

    /**
     * Add ReportInputDuringAgentSpeech attribute.
     *
     * @param bool $reportInputDuringAgentSpeech Whether prompts should be reported
     *                                           to WebSocket server when
     *                                           text-to-speech playing and
     *                                           interrupt is disabled
     */
    public function setReportInputDuringAgentSpeech($reportInputDuringAgentSpeech): self {
        return $this->setAttribute('reportInputDuringAgentSpeech', $reportInputDuringAgentSpeech);
    }

    /**
     * Add ElevenlabsTextNormalization attribute.
     *
     * @param string $elevenlabsTextNormalization When using ElevenLabs as TTS
     *                                            provider, this parameter allows
     *                                            you to enable or disable its text
     *                                            normalization feature
     */
    public function setElevenlabsTextNormalization($elevenlabsTextNormalization): self {
        return $this->setAttribute('elevenlabsTextNormalization', $elevenlabsTextNormalization);
    }

    /**
     * Add InterruptSensitivity attribute.
     *
     * @param string $interruptSensitivity Set the sensitivity of the interrupt
     *                                     feature for speech. The value can be
     *                                     low, medium, or high
     */
    public function setInterruptSensitivity($interruptSensitivity): self {
        return $this->setAttribute('interruptSensitivity', $interruptSensitivity);
    }

    /**
     * Add Debug attribute.
     *
     * @param string $debug Multiple debug options to be used for troubleshooting
     */
    public function setDebug($debug): self {
        return $this->setAttribute('debug', $debug);
    }
}