<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Helpers\SeoHelper;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Page::with(['creator', 'updater']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'published') {
                $query->where('is_published', true);
            } elseif ($status === 'draft') {
                $query->where('is_published', false);
            }
        }

        // Filter by template
        if ($request->filled('template')) {
            $query->where('template', $request->get('template'));
        }

        $pages = $query->orderBy('updated_at', 'desc')->paginate(15);

        // Get available templates
        $templates = $this->getAvailableTemplates();

        return view('admin.pages.index', compact('pages', 'templates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $templates = $this->getAvailableTemplates();
        return view('admin.pages.create', compact('templates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'template' => 'required|string|max:50',
            'is_published' => 'boolean',
            'show_in_menu' => 'boolean',
            'menu_order' => 'nullable|integer|min:0',
            'featured_image' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
        ]);

        // Process meta keywords
        if (!empty($validated['meta_keywords'])) {
            // Check if it's already JSON (from JavaScript processing)
            $keywords = json_decode($validated['meta_keywords'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($keywords)) {
                $validated['meta_keywords'] = $keywords;
            } else {
                // Split by comma if it's a plain string
                $validated['meta_keywords'] = array_map('trim', explode(',', $validated['meta_keywords']));
                $validated['meta_keywords'] = array_filter($validated['meta_keywords']);
            }
        } else {
            $validated['meta_keywords'] = null;
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set creator
        $validated['created_by'] = auth()->id();
        $validated['updated_by'] = auth()->id();

        // Handle published_at
        if ($validated['is_published'] && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $page = Page::create($validated);

        return redirect()
            ->route('admin.pages.show', $page)
            ->with('success', 'Page created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Page $page): View
    {
        $page->load(['creator', 'updater']);
        $seoAnalysis = SeoHelper::getSeoScore($page);
        return view('admin.pages.show', compact('page', 'seoAnalysis'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page): View
    {
        $templates = $this->getAvailableTemplates();
        return view('admin.pages.edit', compact('page', 'templates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Page $page): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('pages', 'slug')->ignore($page->id)],
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'template' => 'required|string|max:50',
            'is_published' => 'boolean',
            'show_in_menu' => 'boolean',
            'menu_order' => 'nullable|integer|min:0',
            'featured_image' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
        ]);

        // Process meta keywords
        if (!empty($validated['meta_keywords'])) {
            // Check if it's already JSON (from JavaScript processing)
            $keywords = json_decode($validated['meta_keywords'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($keywords)) {
                $validated['meta_keywords'] = $keywords;
            } else {
                // Split by comma if it's a plain string
                $validated['meta_keywords'] = array_map('trim', explode(',', $validated['meta_keywords']));
                $validated['meta_keywords'] = array_filter($validated['meta_keywords']);
            }
        } else {
            $validated['meta_keywords'] = null;
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Set updater
        $validated['updated_by'] = auth()->id();

        // Handle published_at
        if ($validated['is_published'] && empty($page->published_at) && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $page->update($validated);

        return redirect()
            ->route('admin.pages.show', $page)
            ->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Page $page): RedirectResponse
    {
        $page->delete();

        return redirect()
            ->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }

    /**
     * Bulk actions for pages.
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $request->validate([
            'action' => 'required|in:publish,unpublish,delete',
            'pages' => 'required|array|min:1',
            'pages.*' => 'exists:pages,id',
        ]);

        $pages = Page::whereIn('id', $request->pages);

        switch ($request->action) {
            case 'publish':
                $pages->update([
                    'is_published' => true,
                    'published_at' => now(),
                    'updated_by' => auth()->id(),
                ]);
                $message = 'Pages published successfully.';
                break;

            case 'unpublish':
                $pages->update([
                    'is_published' => false,
                    'updated_by' => auth()->id(),
                ]);
                $message = 'Pages unpublished successfully.';
                break;

            case 'delete':
                $pages->delete();
                $message = 'Pages deleted successfully.';
                break;
        }

        return redirect()
            ->route('admin.pages.index')
            ->with('success', $message);
    }

    /**
     * Get available page templates.
     */
    private function getAvailableTemplates(): array
    {
        return [
            'default' => 'Default Template',
            'about' => 'About Page',
            'contact' => 'Contact Page',
            'legal' => 'Legal Page',
            'policy' => 'Policy Page',
            'guide' => 'Guide Page',
            'landing' => 'Landing Page',
        ];
    }
}
