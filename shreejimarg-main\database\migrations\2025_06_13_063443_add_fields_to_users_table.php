<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->date('date_of_birth')->nullable()->after('phone');
            $table->enum('gender', ['male', 'female', 'other', 'prefer-not-to-say'])->nullable()->after('date_of_birth');
            $table->string('avatar')->nullable()->after('gender');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('avatar');
            $table->enum('role', ['customer', 'admin', 'manager'])->default('customer')->after('status');
            $table->json('preferences')->nullable()->after('role');
            $table->decimal('total_spent', 10, 2)->default(0)->after('preferences');
            $table->integer('reward_points')->default(0)->after('total_spent');
            $table->enum('membership_level', ['regular', 'silver', 'gold', 'vip'])->default('regular')->after('reward_points');
            $table->timestamp('last_login_at')->nullable()->after('membership_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone', 'date_of_birth', 'gender', 'avatar', 'status', 'role',
                'preferences', 'total_spent', 'reward_points', 'membership_level', 'last_login_at'
            ]);
        });
    }
};
