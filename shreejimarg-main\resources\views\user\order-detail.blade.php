@extends('layouts.app')

@section('title', 'Order Details - ShreeJi Jewelry')
@section('description', 'View your order details and tracking information.')

@section('content')
<!-- Page Header -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="font-playfair display-6 mb-2">Order Details</h1>
                <p class="text-muted mb-0">Order #{{ $order->order_number ?? 'ORD-2024-001' }}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="{{ route('orders') }}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to Orders
                </a>
                @if(isset($order) && $order->canBeCancelled())
                <button class="btn btn-outline-danger cancel-order-btn" data-order-id="{{ $order->id }}">
                    <i class="fas fa-times me-2"></i>Cancel Order
                </button>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Order Information -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            <!-- Order Status -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Order Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6>Current Status</h6>
                                @php
                                    $status = $order->status ?? 'delivered';
                                    $statusClass = match($status) {
                                        'pending' => 'warning',
                                        'confirmed' => 'info',
                                        'processing' => 'primary',
                                        'shipped' => 'success',
                                        'delivered' => 'success',
                                        'cancelled' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }} fs-6">{{ ucfirst($status) }}</span>
                            </div>
                            <div class="col-md-6">
                                <h6>Order Date</h6>
                                <p class="mb-0">{{ $order->created_at ?? 'January 15, 2024' }}</p>
                            </div>
                        </div>
                        
                        <!-- Order Progress -->
                        <div class="mt-4">
                            <div class="progress mb-3" style="height: 8px;">
                                @php
                                    $progress = match($status) {
                                        'pending' => 25,
                                        'confirmed' => 50,
                                        'processing' => 75,
                                        'shipped' => 90,
                                        'delivered' => 100,
                                        'cancelled' => 0,
                                        default => 25
                                    };
                                @endphp
                                <div class="progress-bar bg-{{ $statusClass }}" style="width: {{ $progress }}%"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Order Placed</small>
                                <small class="text-muted">Processing</small>
                                <small class="text-muted">Shipped</small>
                                <small class="text-muted">Delivered</small>
                            </div>
                        </div>
                        
                        @if($status === 'shipped')
                        <div class="mt-3">
                            <button class="btn btn-primary-pink btn-sm">
                                <i class="fas fa-truck me-2"></i>Track Package
                            </button>
                        </div>
                        @endif
                    </div>
                </div>
                
                <!-- Order Items -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Order Items</h5>
                    </div>
                    <div class="card-body">
                        @php
                            // Sample order items for display
                            $orderItems = [
                                [
                                    'product_name' => 'Diamond Solitaire Ring',
                                    'product_sku' => 'DSR001',
                                    'price' => 89500,
                                    'quantity' => 1,
                                    'size' => '7',
                                    'total' => 89500,
                                    'image' => 'https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80'
                                ]
                            ];
                        @endphp
                        
                        @foreach($orderItems as $item)
                        <div class="row align-items-center py-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                            <div class="col-md-2">
                                <img src="{{ $item['image'] }}" alt="{{ $item['product_name'] }}" 
                                     class="img-fluid rounded">
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-1">{{ $item['product_name'] }}</h6>
                                <p class="text-muted mb-1">SKU: {{ $item['product_sku'] }}</p>
                                @if($item['size'])
                                <p class="text-muted mb-0">Size: {{ $item['size'] }}</p>
                                @endif
                            </div>
                            <div class="col-md-2 text-center">
                                <span class="fw-bold">{{ $item['quantity'] }}</span>
                            </div>
                            <div class="col-md-2 text-end">
                                <span class="fw-bold text-primary-pink">₹{{ number_format($item['total']) }}</span>
                                <br>
                                <small class="text-muted">₹{{ number_format($item['price']) }} each</small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $subtotal = 89500;
                            $tax = 16110;
                            $shipping = 0;
                            $total = 105610;
                        @endphp
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>₹{{ number_format($subtotal) }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (18% GST):</span>
                            <span>₹{{ number_format($tax) }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span class="text-success">Free</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total:</span>
                            <span class="text-primary-pink">₹{{ number_format($total) }}</span>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-download me-2"></i>Download Invoice
                            </button>
                            <button class="btn btn-outline-success w-100">
                                <i class="fas fa-redo me-2"></i>Reorder Items
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Shipping Information -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Shipping Address</h5>
                    </div>
                    <div class="card-body">
                        <address class="mb-0">
                            <strong>Priya Sharma</strong><br>
                            123 Jewelry Street<br>
                            Bandra West<br>
                            Mumbai, Maharashtra 400050<br>
                            <abbr title="Phone">P:</abbr> +91 98765 43210
                        </address>
                    </div>
                </div>
                
                <!-- Payment Information -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Payment Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Payment Method:</span>
                            <span>Credit Card</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Payment Status:</span>
                            <span class="badge bg-success">Paid</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Transaction ID:</span>
                            <span class="text-muted">TXN_123456789</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Customer Support -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h3 class="font-playfair mb-3">Need Help?</h3>
                <p class="text-muted mb-4">
                    Our customer support team is here to help with any questions about your order.
                </p>
                <div class="row g-3">
                    <div class="col-md-4">
                        <a href="{{ route('contact') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="tel:+919876543210" class="btn btn-outline-primary w-100">
                            <i class="fas fa-phone me-2"></i>Call Us
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="#" class="btn btn-outline-primary w-100">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    // Cancel order functionality
    document.querySelector('.cancel-order-btn')?.addEventListener('click', function() {
        const orderId = this.dataset.orderId;
        
        if (confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
            fetch(`/orders/${orderId}/cancel`, {
                method: 'PUT',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Order cancelled successfully!');
                    location.reload();
                } else {
                    alert(data.message || 'Error cancelling order');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error cancelling order');
            });
        }
    });
</script>
@endpush
