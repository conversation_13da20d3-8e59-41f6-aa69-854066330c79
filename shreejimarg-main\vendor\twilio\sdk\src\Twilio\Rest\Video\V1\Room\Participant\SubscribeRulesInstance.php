<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1\Room\Participant;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $participantSid
 * @property string|null $roomSid
 * @property string[]|null $rules
 * @property \DateTime|null $dateCreated
 * @property \DateTime|null $dateUpdated
 */
class SubscribeRulesInstance extends InstanceResource
{
    /**
     * Initialize the SubscribeRulesInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $roomSid The SID of the Room resource where the subscribe rules to fetch apply.
     * @param string $participantSid The SID of the Participant resource with the subscribe rules to fetch.
     */
    public function __construct(Version $version, array $payload, string $roomSid, string $participantSid)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'participantSid' => Values::array_get($payload, 'participant_sid'),
            'roomSid' => Values::array_get($payload, 'room_sid'),
            'rules' => Values::array_get($payload, 'rules'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
        ];

        $this->solution = ['roomSid' => $roomSid, 'participantSid' => $participantSid, ];
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Video.V1.SubscribeRulesInstance]';
    }
}

