<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "Creating test user for email login...\n";

$user = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Test User',
        'password' => bcrypt('password123'),
        'phone' => '9876543210',
        'role' => 'customer',
        'is_mobile_verified' => true,
        'email_verified_at' => now()
    ]
);

echo "Test user ready:\n";
echo "Email: " . $user->email . "\n";
echo "Password: password123\n";
echo "You can now test email login at /login\n";
