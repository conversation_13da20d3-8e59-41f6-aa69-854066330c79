<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\TwiML\Voice;

use <PERSON>wi<PERSON>\TwiML\TwiML;

class Application extends TwiML {
    /**
     * Application constructor.
     *
     * @param string $applicationSid Application sid
     * @param array $attributes Optional attributes
     */
    public function __construct($applicationSid = null, $attributes = []) {
        parent::__construct('Application', $applicationSid, $attributes);
    }

    /**
     * Add ApplicationSid child.
     *
     * @param string $sid Application sid to dial
     * @return ApplicationSid Child element.
     */
    public function applicationSid($sid): ApplicationSid {
        return $this->nest(new ApplicationSid($sid));
    }

    /**
     * Add Parameter child.
     *
     * @param array $attributes Optional attributes
     * @return Parameter Child element.
     */
    public function parameter($attributes = []): Parameter {
        return $this->nest(new Parameter($attributes));
    }

    /**
     * Add Url attribute.
     *
     * @param string $url TwiML URL
     */
    public function setUrl($url): self {
        return $this->setAttribute('url', $url);
    }

    /**
     * Add Method attribute.
     *
     * @param string $method TwiML URL Method
     */
    public function setMethod($method): self {
        return $this->setAttribute('method', $method);
    }

    /**
     * Add StatusCallbackEvent attribute.
     *
     * @param string[] $statusCallbackEvent Events to trigger status callback
     */
    public function setStatusCallbackEvent($statusCallbackEvent): self {
        return $this->setAttribute('statusCallbackEvent', $statusCallbackEvent);
    }

    /**
     * Add StatusCallback attribute.
     *
     * @param string $statusCallback Status Callback URL
     */
    public function setStatusCallback($statusCallback): self {
        return $this->setAttribute('statusCallback', $statusCallback);
    }

    /**
     * Add StatusCallbackMethod attribute.
     *
     * @param string $statusCallbackMethod Status Callback URL Method
     */
    public function setStatusCallbackMethod($statusCallbackMethod): self {
        return $this->setAttribute('statusCallbackMethod', $statusCallbackMethod);
    }

    /**
     * Add CustomerId attribute.
     *
     * @param string $customerId Identity of the customer calling application
     */
    public function setCustomerId($customerId): self {
        return $this->setAttribute('customerId', $customerId);
    }

    /**
     * Add CopyParentTo attribute.
     *
     * @param bool $copyParentTo Copy parent call To field to called application
     *                           side, otherwise use the application sid as To field
     */
    public function setCopyParentTo($copyParentTo): self {
        return $this->setAttribute('copyParentTo', $copyParentTo);
    }
}