@extends('layouts.admin')

@section('title', 'Manage Orders - Admin - ShreeJi Jewelry')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Order Management</h1>
        <p class="mb-0 text-muted">Manage customer orders and fulfillment</p>
    </div>
    <div>
        <a href="{{ route('admin.orders.tracking.index') }}" class="btn btn-primary-pink">
            <i class="fas fa-truck me-2"></i>Order Tracking
        </a>
    </div>
</div>

<!-- Quick Stats -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">247</h4>
                        <p class="text-muted mb-0">Total Orders</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">23</h4>
                        <p class="text-muted mb-0">Pending</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">45</h4>
                        <p class="text-muted mb-0">Shipped</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h4 class="mb-0">179</h4>
                        <p class="text-muted mb-0">Delivered</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-center">
            <div class="col-md-3">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Search orders...">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" placeholder="From Date">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" placeholder="To Date">
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-1"></i>Filter
                </button>
            </div>
            <div class="col-md-1">
                <button class="btn btn-outline-success w-100" title="Export">
                    <i class="fas fa-download"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Recent Orders</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>Export
            </button>
            <button class="btn btn-outline-primary btn-sm">
                <i class="fas fa-sync me-1"></i>Refresh
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Items</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Payment</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>#ORD-2024-047</strong></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary-pink text-white rounded-circle d-flex align-items-center justify-content-center me-2" 
                                     style="width: 30px; height: 30px; font-size: 12px;">
                                    P
                                </div>
                                <div>
                                    <div>Priya Sharma</div>
                                    <small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>3 items</td>
                        <td><strong>₹89,500</strong></td>
                        <td><span class="badge bg-warning">Processing</span></td>
                        <td><span class="badge bg-success">Paid</span></td>
                        <td>Jan 28, 2024</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.orders.tracking.show', 1) }}" class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-success btn-sm" title="Update Status">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" title="Print">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <!-- Add more sample rows here -->
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-info-circle text-muted me-2"></i>
                            <span class="text-muted">This is a placeholder view. Real order data will be displayed here.</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="text-muted mb-0">Showing 1 to 10 of 247 orders</p>
            </div>
            <div class="col-md-6">
                <nav aria-label="Orders pagination">
                    <ul class="pagination justify-content-end mb-0">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
@endsection
