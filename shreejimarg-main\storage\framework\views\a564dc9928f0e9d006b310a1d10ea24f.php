<?php $__env->startSection('title', 'Create Account - ShreeJi Jewelry'); ?>
<?php $__env->startSection('description', 'Create your ShreeJi account to enjoy exclusive offers, track orders, and save your favorite jewelry pieces.'); ?>

<?php $__env->startSection('content'); ?>
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <h2 class="font-playfair text-primary-pink mb-2">
                                <i class="fas fa-gem me-2"></i>ShreeJi
                            </h2>
                            <h4 class="font-playfair mb-3">Create Your Account</h4>
                            <p class="text-muted">Join our exclusive jewelry community</p>
                        </div>
                        
                        <!-- Registration Form -->
                        <form id="registerForm">
                            <?php echo csrf_field(); ?>
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control form-control-lg" id="name" name="name" required>
                                <div class="invalid-feedback" id="nameError"></div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control form-control-lg" id="email" name="email" required>
                                <div class="invalid-feedback" id="emailError"></div>
                            </div>

                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control form-control-lg" id="phone" name="phone" required>
                                <div class="invalid-feedback" id="phoneError"></div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="passwordError"></div>
                                <div class="form-text">
                                    Password must be at least 6 characters long.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Confirm Password</label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password_confirmation" name="password_confirmation" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                                        <i class="fas fa-eye" id="password_confirmationToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="password_confirmationError"></div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" value="1" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="<?php echo e(route('terms-of-service')); ?>" class="text-primary-pink">Terms of Service</a>
                                        and <a href="<?php echo e(route('privacy-policy')); ?>" class="text-primary-pink">Privacy Policy</a>
                                    </label>
                                    <div class="invalid-feedback" id="termsError"></div>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter" value="1" checked>
                                    <label class="form-check-label" for="newsletter">
                                        Subscribe to our newsletter for exclusive offers and new arrivals
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary-pink w-100 btn-lg mb-3" id="registerBtn">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </form>
                        
                        <!-- Social Registration -->
                        <div class="text-center mb-4">
                            <p class="text-muted mb-3">Or sign up with</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <button class="btn btn-outline-danger flex-fill">
                                    <i class="fab fa-google me-2"></i>Google
                                </button>
                                <button class="btn btn-outline-primary flex-fill">
                                    <i class="fab fa-facebook-f me-2"></i>Facebook
                                </button>
                            </div>
                        </div>
                        
                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                Already have an account? 
                                <a href="<?php echo e(route('login')); ?>" class="text-primary-pink text-decoration-none fw-semibold">Sign In</a>
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Account Benefits -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body p-4">
                        <h6 class="font-playfair mb-3 text-center">Account Benefits</h6>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-crown text-primary-pink me-2"></i>
                                    <small>VIP Member Discounts</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-gift text-primary-pink me-2"></i>
                                    <small>Birthday Special Offers</small>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-bell text-primary-pink me-2"></i>
                                    <small>New Collection Alerts</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-history text-primary-pink me-2"></i>
                                    <small>Order History & Tracking</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-heart text-primary-pink me-2"></i>
                                    <small>Wishlist & Favorites</small>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-headset text-primary-pink me-2"></i>
                                    <small>Priority Customer Support</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function togglePassword(fieldId) {
        const passwordInput = document.getElementById(fieldId);
        const toggleIcon = document.getElementById(fieldId + 'ToggleIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
    
    function validatePassword(password) {
        const minLength = 6;
        return password.length >= minLength;
    }
    
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const registerBtn = document.getElementById('registerBtn');

        // Clear previous errors
        clearErrors();

        // Show loading state
        registerBtn.disabled = true;
        registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';

        fetch('<?php echo e(route("register.post")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            } else {
                showAlert(data.message, 'danger');
                if (data.errors) {
                    showErrors(data.errors);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred. Please try again.', 'danger');
        })
        .finally(() => {
            // Reset button state
            registerBtn.disabled = false;
            registerBtn.innerHTML = '<i class="fas fa-user-plus me-2"></i>Create Account';
        });
    });

    function clearErrors() {
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
    }

    function showErrors(errors) {
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(field);
            const errorDiv = document.getElementById(field + 'Error');
            if (input && errorDiv) {
                input.classList.add('is-invalid');
                errorDiv.textContent = errors[field][0];
            }
        });
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    // Real-time password validation
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const isValid = validatePassword(password);
        
        if (password.length > 0) {
            if (isValid) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // Real-time password confirmation validation
    document.getElementById('password_confirmation').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;

        if (confirmPassword.length > 0) {
            if (password === confirmPassword) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shreejimarg-main\resources\views/auth/register.blade.php ENDPATH**/ ?>