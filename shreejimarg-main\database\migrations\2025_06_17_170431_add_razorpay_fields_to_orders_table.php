<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('payment_gateway_order_id')->nullable()->after('payment_transaction_id');
            $table->string('payment_gateway')->default('razorpay')->after('payment_gateway_order_id');
            $table->json('payment_details')->nullable()->after('payment_gateway');
            $table->string('razorpay_payment_id')->nullable()->after('payment_details');
            $table->string('razorpay_signature')->nullable()->after('razorpay_payment_id');
            $table->timestamp('payment_completed_at')->nullable()->after('razorpay_signature');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'payment_gateway_order_id',
                'payment_gateway',
                'payment_details',
                'razorpay_payment_id',
                'razorpay_signature',
                'payment_completed_at'
            ]);
        });
    }
};
