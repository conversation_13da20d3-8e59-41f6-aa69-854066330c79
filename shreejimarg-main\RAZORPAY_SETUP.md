# Razorpay Payment Gateway Setup

## Current Issue
You're getting "authentication failed" errors because the Razorpay credentials in your `.env` file are placeholder values, not real test credentials from Razorpay.

## How to Fix

### Step 1: Get Razorpay Test Credentials

1. **Sign up for Razorpay Account**
   - Go to https://razorpay.com/
   - Click "Sign Up" and create a free account
   - Complete the verification process

2. **Get Test API Keys**
   - Login to your Razorpay Dashboard
   - Go to Settings → API Keys
   - Generate Test API Keys (not Live keys)
   - You'll get:
     - `Key ID` (starts with `rzp_test_`)
     - `Key Secret` (keep this secret)

### Step 2: Update Your .env File

Replace the placeholder values in your `.env` file:

```env
# Replace these placeholder values with your real Razorpay test credentials
RAZORPAY_KEY_ID=rzp_test_YOUR_ACTUAL_KEY_ID_HERE
RAZORPAY_KEY_SECRET=YOUR_ACTUAL_SECRET_KEY_HERE
RAZORPAY_WEBHOOK_SECRET=YOUR_WEBHOOK_SECRET_HERE
```

### Step 3: Set Up Webhooks (Optional but Recommended)

1. In Razorpay Dashboard, go to Settings → Webhooks
2. Create a new webhook with URL: `https://yourdomain.com/payment/webhook`
3. Select events: `payment.captured`, `payment.failed`, `order.paid`
4. Copy the webhook secret and add it to your `.env` file

### Step 4: Test the Payment Flow

1. Clear your application cache:
   ```bash
   php artisan config:clear
   php artisan cache:clear
   ```

2. Try the payment process again
3. Use Razorpay test cards for testing:
   - **Success Card**: 4111 1111 1111 1111
   - **CVV**: Any 3 digits
   - **Expiry**: Any future date
   - **Name**: Any name

## What We Fixed

1. **Cart Preservation**: Cart is no longer cleared immediately when order is created
2. **Cart Restoration**: If payment fails, cart items are automatically restored
3. **Better Error Messages**: More user-friendly error messages for payment failures
4. **Proper Error Handling**: Authentication errors are handled gracefully

## Test Cards for Development

### Success Cards
- **Visa**: 4111 1111 1111 1111
- **Mastercard**: 5555 5555 5554 4444
- **CVV**: 123
- **Expiry**: 12/25

### Failure Cards
- **Declined**: 4000 0000 0000 0002

### Test UPI ID
- **UPI**: success@razorpay

## Important Notes

1. **Never use Live credentials in development**
2. **Keep your secret keys secure**
3. **Test thoroughly before going live**
4. **Enable webhooks for production**

## Current Payment Flow

1. User adds items to cart
2. User proceeds to checkout
3. Order is created (cart is NOT cleared yet)
4. Razorpay payment is initiated
5. On successful payment:
   - Cart is cleared
   - User stats are updated
   - Order is confirmed
   - Email is sent
6. On failed payment:
   - Cart is restored
   - Order is marked as failed
   - User-friendly error message is shown

## Need Help?

If you continue to have issues:
1. Check Razorpay dashboard for error logs
2. Check Laravel logs in `storage/logs/laravel.log`
3. Ensure your domain is accessible (for webhooks)
4. Contact Razorpay support if needed

## Testing the Fix

You can run our comprehensive payment tests:
```bash
php artisan test tests/Feature/PaymentEndToEndTest.php
php artisan test tests/Feature/PaymentIntegrationTest.php
```

These tests verify that the payment system handles failures gracefully and preserves the user experience.
