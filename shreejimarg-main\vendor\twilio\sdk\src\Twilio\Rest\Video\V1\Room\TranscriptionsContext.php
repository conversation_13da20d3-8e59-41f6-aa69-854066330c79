<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1\Room;

use Twilio\Exceptions\TwilioException;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;
use Twilio\InstanceContext;


class TranscriptionsContext extends InstanceContext
    {
    /**
     * Initialize the TranscriptionsContext
     *
     * @param Version $version Version that contains the resource
     * @param string $roomSid The SID of the room new transcriptions resource to be created.
     * @param string $ttid The Twilio type id of the transcriptions resource to fetch.
     */
    public function __construct(
        Version $version,
        $roomSid,
        $ttid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'roomSid' =>
            $roomSid,
        'ttid' =>
            $ttid,
        ];

        $this->uri = '/Rooms/' . \rawurlencode($roomSid)
        .'/Transcriptions/' . \rawurlencode($ttid)
        .'';
    }

    /**
     * Fetch the TranscriptionsInstance
     *
     * @return TranscriptionsInstance Fetched TranscriptionsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): TranscriptionsInstance
    {

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->fetch('GET', $this->uri, [], [], $headers);

        return new TranscriptionsInstance(
            $this->version,
            $payload,
            $this->solution['roomSid'],
            $this->solution['ttid']
        );
    }


    /**
     * Update the TranscriptionsInstance
     *
     * @param array|Options $options Optional Arguments
     * @return TranscriptionsInstance Updated TranscriptionsInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): TranscriptionsInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'Status' =>
                $options['status'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->update('POST', $this->uri, [], $data, $headers);

        return new TranscriptionsInstance(
            $this->version,
            $payload,
            $this->solution['roomSid'],
            $this->solution['ttid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Video.V1.TranscriptionsContext ' . \implode(' ', $context) . ']';
    }
}
