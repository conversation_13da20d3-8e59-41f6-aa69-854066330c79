@extends('layouts.admin')

@section('title', 'Add New Product - Admin - ShreeJi Jewelry')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Add New Product</h1>
        <p class="mb-0 text-muted">Create a new jewelry product for your inventory</p>
    </div>
    <div>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Products
        </a>
    </div>
</div>

<!-- Product Form -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <form id="productForm" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sku" class="form-label">SKU <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="sku" name="sku" required>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stock_quantity" class="form-label">Stock Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" required>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Products with 0 stock will be marked as "Out of Stock" and won't show "Add to Cart" button
                            </small>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">Regular Price (₹) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sale_price" class="form-label">Sale Price (₹)</label>
                            <input type="number" class="form-control" id="sale_price" name="sale_price" step="0.01" min="0">
                            <small class="form-text text-muted">Leave empty if not on sale</small>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <!-- Descriptions -->
                    <div class="mb-3">
                        <label for="short_description" class="form-label">Short Description</label>
                        <textarea class="form-control" id="short_description" name="short_description" rows="2" placeholder="Brief product description for listings"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Full Description <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="4" placeholder="Detailed product description"></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <!-- Jewelry Specifications -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">Jewelry Specifications</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="weight" class="form-label">Weight (grams)</label>
                                    <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="metal_type" class="form-label">Metal Type</label>
                                    <select class="form-select" id="metal_type" name="metal_type">
                                        <option value="">Select Metal Type</option>
                                        <option value="gold">Gold</option>
                                        <option value="silver">Silver</option>
                                        <option value="platinum">Platinum</option>
                                        <option value="rose_gold">Rose Gold</option>
                                        <option value="white_gold">White Gold</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="metal_purity" class="form-label">Metal Purity</label>
                                    <select class="form-select" id="metal_purity" name="metal_purity">
                                        <option value="">Select Purity</option>
                                        <option value="14k">14K</option>
                                        <option value="18k">18K</option>
                                        <option value="22k">22K</option>
                                        <option value="24k">24K</option>
                                        <option value="925">925 Sterling Silver</option>
                                        <option value="950">950 Platinum</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="stone_type" class="form-label">Stone Type</label>
                                    <input type="text" class="form-control" id="stone_type" name="stone_type" placeholder="e.g., Diamond, Ruby, Emerald">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="stone_weight" class="form-label">Stone Weight (carats)</label>
                                    <input type="number" class="form-control" id="stone_weight" name="stone_weight" step="0.01" min="0">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="certification" class="form-label">Certification</label>
                                    <input type="text" class="form-control" id="certification" name="certification" placeholder="e.g., GIA, IGI">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Images -->
                    <div class="mb-3">
                        <label for="images" class="form-label">Product Images</label>
                        <input type="file" class="form-control" id="images" name="images[]" multiple accept="image/*">
                        <small class="form-text text-muted">Select multiple images. First image will be the main product image.</small>
                        <div class="invalid-feedback"></div>
                        <div id="imagePreview" class="mt-3 row"></div>
                    </div>

                    <!-- Additional Options -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sizes" class="form-label">Available Sizes</label>
                            <input type="text" class="form-control" id="sizes" name="sizes" placeholder="e.g., 6,7,8,9,10">
                            <small class="form-text text-muted">Comma-separated values</small>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="specifications" class="form-label">Additional Specifications</label>
                            <textarea class="form-control" id="specifications" name="specifications" rows="2" placeholder="Any additional specifications (JSON format)"></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <!-- Product Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">Product Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                <label class="form-check-label" for="is_featured">
                                    <strong>Featured Product</strong>
                                    <small class="d-block text-muted">Mark this product as featured to display it prominently on the homepage</small>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary-pink" id="submitBtn">
                            <i class="fas fa-save me-2"></i>Create Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .image-preview {
        position: relative;
        display: inline-block;
        margin: 5px;
    }
    
    .image-preview img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #dee2e6;
    }
    
    .image-preview .remove-image {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        font-size: 12px;
        cursor: pointer;
    }
</style>
@endpush

@push('scripts')
<!-- Include CKEditor 5 Classic -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor for product description
    let descriptionEditor;
    ClassicEditor.create(document.querySelector('#description'), {
        toolbar: {
            items: [
                'heading', '|',
                'bold', 'italic', '|',
                'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'blockQuote', '|',
                'undo', 'redo'
            ]
        },
        heading: {
            options: [
                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                { model: 'heading2', view: 'h2', title: 'Heading', class: 'ck-heading_heading2' }
            ]
        }
    })
    .then(editor => {
        descriptionEditor = editor;
    })
    .catch(error => {
        console.error('CKEditor initialization error:', error);
    });
    const form = document.getElementById('productForm');
    const submitBtn = document.getElementById('submitBtn');
    const imageInput = document.getElementById('images');
    const imagePreview = document.getElementById('imagePreview');
    
    // Image preview functionality
    imageInput.addEventListener('change', function(e) {
        imagePreview.innerHTML = '';
        const files = Array.from(e.target.files);
        
        files.forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-auto';
                    col.innerHTML = `
                        <div class="image-preview">
                            <img src="${e.target.result}" alt="Preview">
                            <button type="button" class="remove-image" data-index="${index}">×</button>
                        </div>
                    `;
                    imagePreview.appendChild(col);
                };
                reader.readAsDataURL(file);
            }
        });
    });
    
    // Remove image functionality
    imagePreview.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-image')) {
            const index = parseInt(e.target.dataset.index);
            const dt = new DataTransfer();
            const files = Array.from(imageInput.files);
            
            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });
            
            imageInput.files = dt.files;
            imageInput.dispatchEvent(new Event('change'));
        }
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate CKEditor content
        if (descriptionEditor) {
            const editorData = descriptionEditor.getData().trim();
            if (!editorData || editorData === '<p>&nbsp;</p>' || editorData === '<p></p>') {
                alert('Please enter a product description.');
                descriptionEditor.editing.view.focus();
                return;
            }
            // Update the hidden textarea with CKEditor content
            document.getElementById('description').value = editorData;
        }

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';

        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        
        const formData = new FormData(form);
        
        // Convert sizes to array
        const sizesInput = document.getElementById('sizes').value;
        if (sizesInput) {
            const sizesArray = sizesInput.split(',').map(s => s.trim()).filter(s => s);
            formData.delete('sizes');
            sizesArray.forEach((size, index) => {
                formData.append(`sizes[${index}]`, size);
            });
        }

        // Convert specifications to array if provided
        const specsInput = document.getElementById('specifications').value;
        if (specsInput) {
            formData.delete('specifications');
            try {
                const specsObj = JSON.parse(specsInput);
                Object.keys(specsObj).forEach(key => {
                    formData.append(`specifications[${key}]`, specsObj[key]);
                });
            } catch (e) {
                // If not valid JSON, treat as plain text description
                formData.append('specifications[description]', specsInput);
            }
        }
        
        fetch('{{ route("admin.products.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw { status: response.status, data: errorData };
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                form.insertBefore(alert, form.firstChild);

                // Redirect after a short delay
                setTimeout(() => {
                    window.location.href = data.redirect_url || '{{ route("admin.products.index") }}';
                }, 1500);
            } else {
                throw { status: 400, data: data };
            }
        })
        .catch(error => {
            console.error('Error:', error);

            let errorMessage = 'An error occurred while creating the product.';
            let validationErrors = null;

            if (error.data) {
                errorMessage = error.data.message || errorMessage;
                validationErrors = error.data.errors;
            } else if (error.message) {
                errorMessage = error.message;
            }

            // Show error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                ${errorMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            form.insertBefore(alert, form.firstChild);

            // Handle validation errors
            if (validationErrors) {
                Object.keys(validationErrors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                        const feedback = input.parentNode.querySelector('.invalid-feedback');
                        if (feedback) {
                            feedback.textContent = validationErrors[field][0];
                        }
                    }
                });
            }

            // Scroll to top to show error
            window.scrollTo(0, 0);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Product';
        });
    });
});
</script>
@endpush
