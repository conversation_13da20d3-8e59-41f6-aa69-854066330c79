<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\TwiML\Voice;

use <PERSON><PERSON><PERSON>\TwiML\TwiML;

class SsmlBreak extends TwiML {
    /**
     * SsmlBreak constructor.
     *
     * @param array $attributes Optional attributes
     */
    public function __construct($attributes = []) {
        parent::__construct('break', null, $attributes);
    }

    /**
     * Add Strength attribute.
     *
     * @param string $strength Set a pause based on strength
     */
    public function setStrength($strength): self {
        return $this->setAttribute('strength', $strength);
    }

    /**
     * Add Time attribute.
     *
     * @param string $time Set a pause to a specific length of time in seconds or
     *                     milliseconds, available values: [number]s, [number]ms
     */
    public function setTime($time): self {
        return $this->setAttribute('time', $time);
    }
}