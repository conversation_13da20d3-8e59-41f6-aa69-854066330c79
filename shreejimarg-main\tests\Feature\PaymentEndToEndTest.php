<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\Category;
use App\Models\OrderItem;
use App\Services\RazorpayService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;

class PaymentEndToEndTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $order;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set test environment variables
        config([
            'services.razorpay.key_id' => 'rzp_test_1DP5mmOlF5G5ag',
            'services.razorpay.key_secret' => 'thisissecretkey',
            'services.razorpay.webhook_secret' => 'whsec_test_webhook_secret'
        ]);
        
        // Create test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        // Create test category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::create([
            'category_id' => $category->id,
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-001',
            'description' => 'Test product description',
            'price' => 100.00,
            'stock_quantity' => 10,
            'in_stock' => true,
            'status' => 'active',
            'images' => ['test.jpg'],
            'specifications' => ['test' => 'value'],
            'sizes' => ['M'],
        ]);

        // Create test order
        $this->order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST_' . time(),
            'subtotal' => 100.00,
            'tax_amount' => 0.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'razorpay',
            'shipping_address' => [
                'name' => 'Test User',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456'
            ],
            'billing_address' => [
                'name' => 'Test User',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456'
            ]
        ]);

        // Create order item
        OrderItem::create([
            'order_id' => $this->order->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'product_sku' => $this->product->sku,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00
        ]);
    }

    /** @test */
    public function it_handles_razorpay_authentication_failure_gracefully()
    {
        $razorpayService = new RazorpayService();

        $result = $razorpayService->createOrder($this->order);

        // With invalid test credentials, we expect authentication failure
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid request parameters', $result['error']);
        $this->assertEquals('Authentication failed', $result['message']);

        // Order should not be updated when API call fails
        $this->order->refresh();
        $this->assertNull($this->order->payment_gateway_order_id);
    }

    /** @test */
    public function it_can_get_test_card_details_from_service()
    {
        $razorpayService = new RazorpayService();
        
        $result = $razorpayService->getTestCardDetails();
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('success_cards', $result);
        $this->assertArrayHasKey('failure_cards', $result);
        $this->assertArrayHasKey('test_upi_id', $result);
        $this->assertArrayHasKey('test_netbanking', $result);
        
        // Check success cards structure
        $this->assertIsArray($result['success_cards']);
        $this->assertNotEmpty($result['success_cards']);
        
        foreach ($result['success_cards'] as $card) {
            $this->assertArrayHasKey('number', $card);
            $this->assertArrayHasKey('cvv', $card);
            $this->assertArrayHasKey('expiry', $card);
            $this->assertArrayHasKey('name', $card);
        }
    }

    /** @test */
    public function it_handles_payment_order_creation_failure_via_controller()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        // With invalid credentials, the controller should return an error
        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Authentication failed'
            ]);
    }

    /** @test */
    public function it_handles_payment_verification_with_invalid_signature()
    {
        // First create a Razorpay order
        $this->order->update([
            'payment_gateway_order_id' => 'order_test_123'
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/verify', [
                'razorpay_order_id' => 'order_test_123',
                'razorpay_payment_id' => 'pay_test_123',
                'razorpay_signature' => 'invalid_signature',
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Payment verification failed. Your cart has been restored.'
            ]);

        // Order status should be updated to failed due to invalid signature
        $this->order->refresh();
        $this->assertEquals('pending', $this->order->status);
        $this->assertEquals('failed', $this->order->payment_status);
    }

    /** @test */
    public function it_can_simulate_complete_payment_flow()
    {
        Mail::fake();

        // Step 1: Try to create payment order (will fail with test credentials)
        $createResponse = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $createResponse->assertStatus(400);
        $createData = $createResponse->json();
        $this->assertFalse($createData['success']);

        // Step 2: Simulate payment failure (since we can't actually complete payment with test credentials)
        $failResponse = $this->actingAs($this->user)
            ->postJson('/payment/failed', [
                'order_id' => $this->order->id
            ]);

        $failResponse->assertStatus(200)
            ->assertJson([
                'success' => false,
                'message' => 'Payment failed. Your cart has been restored.'
            ]);

        // Verify order status updated to failed
        $this->order->refresh();
        $this->assertEquals('failed', $this->order->payment_status);
        $this->assertEquals('cancelled', $this->order->status);

        // No email should be sent for failed payment
        Mail::assertNothingSent();
    }

    /** @test */
    public function it_validates_order_ownership_in_payment_flow()
    {
        // Create another user
        $otherUser = User::create([
            'name' => 'Other User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        // Try to create payment order for another user's order
        $response = $this->actingAs($otherUser)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Unauthorized access to order'
            ]);
    }

    /** @test */
    public function it_prevents_duplicate_payment_for_paid_orders()
    {
        // Mark order as already paid
        $this->order->update([
            'payment_status' => 'paid',
            'status' => 'confirmed'
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/payment/create-order', [
                'order_id' => $this->order->id
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Order is already paid'
            ]);
    }
}
